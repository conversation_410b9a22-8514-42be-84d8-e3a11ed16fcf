#!/usr/bin/env python3
"""
Test the fast chatbot
"""

import requests
import json
import time

def test_fast_chatbot():
    """Test the fast AI chatbot"""
    
    print("⚡ TESTING FAST MARKET PRICE CHATBOT")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5001"
    
    # Test health first
    try:
        health_response = requests.get(f"{base_url}/api/health", timeout=5)
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"✅ {health_data['service']} v{health_data['version']} is running")
        else:
            print("❌ Health check failed")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {str(e)}")
        return
    
    # Test queries
    test_queries = [
        {
            "query": "ERCOT LMP statistics this week",
            "description": "Basic ISO and data type recognition"
        },
        {
            "query": "What is the RMSE for PJM north hub?",
            "description": "RMSE calculation with hub specification"
        },
        {
            "query": "Show me volatility for CAISO real-time prices",
            "description": "Volatility analysis with RT abbreviation"
        },
        {
            "query": "Peak analysis for NYISO",
            "description": "Peak vs off-peak analysis"
        },
        {
            "query": "MISO balance day prices statistics",
            "description": "Balance day data type recognition"
        },
        {
            "query": "NDDAM price trends for ERCOT south hub",
            "description": "Next day day-ahead market with hub"
        }
    ]
    
    successful_tests = 0
    total_tests = len(test_queries)
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}/{total_tests}: {test_case['description']}")
        print(f"Query: '{test_case['query']}'")
        print("-" * 50)
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/chat",
                json={"message": test_case['query']},
                timeout=10  # Much shorter timeout for fast service
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Response received in {response_time:.2f}s")
                
                # Check if we got a meaningful response
                if 'response' in result and len(result['response']) > 50:
                    print("📊 Fast Analysis Response:")
                    
                    # Show first few lines of response
                    response_lines = result['response'].split('\n')
                    for line in response_lines[:6]:
                        if line.strip():
                            print(f"   {line}")
                    
                    # Show additional info
                    if result.get('data_summary'):
                        data_summary = result['data_summary']
                        print(f"\n📈 Data: {data_summary.get('data_points', 'N/A')} points")
                        print(f"📅 Range: {data_summary.get('date_range', 'N/A')}")
                        print(f"💰 Price Range: {data_summary.get('price_range', 'N/A')}")
                    
                    if result.get('confidence'):
                        confidence = result['confidence']
                        print(f"🎯 Confidence: {confidence:.1%}")
                    
                    # Show query parsing results
                    if result.get('query_params'):
                        params = result['query_params']
                        print(f"🔍 Parsed - ISO: {params.get('iso', 'N/A')}, Type: {params.get('data_type', 'N/A')}, Analysis: {params.get('analysis_type', 'N/A')}")
                    
                    successful_tests += 1
                    
                else:
                    print("⚠️ Response too short or missing")
                    print(f"   Response: {result.get('response', 'No response')[:100]}...")
                    
            else:
                print(f"❌ HTTP Error {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print("⏰ Request timed out (>10s)")
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        # Small delay between requests
        time.sleep(0.5)
    
    # Summary
    print("\n" + "=" * 50)
    print("⚡ FAST CHATBOT TEST SUMMARY")
    print("=" * 50)
    
    success_rate = (successful_tests / total_tests) * 100
    
    print(f"✅ Successful responses: {successful_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 EXCELLENT! Your fast chatbot is working perfectly!")
        print("\n🚀 Key Features Demonstrated:")
        print("   ⚡ Lightning-fast rule-based parsing")
        print("   🎯 Intelligent sensor discovery and matching")
        print("   📊 Real-time analysis generation")
        print("   🔍 Complex query parsing (ISOs, data types, hubs, metrics)")
        print("   💨 Sub-second response times")
        
    elif success_rate >= 60:
        print("✅ GOOD! Most features are working, some fine-tuning needed")
        
    else:
        print("⚠️ NEEDS IMPROVEMENT! Check the error messages above")
    
    print(f"\n🌐 Web Interface: {base_url}")
    print("💡 Try these example queries in the web interface:")
    print("   • 'ERCOT LMP statistics this week'")
    print("   • 'PJM north hub RMSE calculation'")
    print("   • 'CAISO real-time price volatility'")
    print("   • 'NYISO peak analysis'")
    print("   • 'MISO balance day price trends'")

def test_specific_features():
    """Test specific fast parsing features"""
    print("\n\n🔬 TESTING SPECIFIC FAST PARSING FEATURES")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5001"
    
    # Test abbreviation understanding
    abbreviation_tests = [
        "RT prices for ERCOT",  # Real-time
        "DA market for PJM",    # Day-ahead
        "NDDAM for CAISO",      # Next day day-ahead market
        "bal day for MISO"      # Balance day
    ]
    
    print("🔤 Testing abbreviation understanding:")
    for query in abbreviation_tests:
        try:
            response = requests.post(
                f"{base_url}/api/chat",
                json={"message": query},
                timeout=5
            )
            
            if response.status_code == 200:
                result = response.json()
                confidence = result.get('confidence', 0)
                if confidence > 0.5:
                    print(f"   ✅ '{query}' → Confidence: {confidence:.1%}")
                else:
                    print(f"   ⚠️ '{query}' → Low confidence: {confidence:.1%}")
            else:
                print(f"   ❌ '{query}' → Error {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ '{query}' → Exception: {str(e)}")
        
        time.sleep(0.2)
    
    print(f"\n🎊 Fast parsing test completed!")

def main():
    """Run all tests"""
    print(f"🚀 FAST CHATBOT COMPREHENSIVE TEST SUITE")
    print(f"Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Test main chatbot functionality
        test_fast_chatbot()
        
        # Test specific features
        test_specific_features()
        
        print("\n\n🎊 ALL TESTS COMPLETED!")
        print("Your fast market price chatbot is ready for production use!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Test suite failed: {str(e)}")

if __name__ == "__main__":
    main()
