import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.linear_model import LinearRegression
import logging

logger = logging.getLogger(__name__)

class DataAnalyzer:
    """Service for performing statistical analysis on market price data"""
    
    def __init__(self):
        self.analysis_functions = {
            'rmse': self._calculate_rmse,
            'mae': self._calculate_mae,
            'volatility': self._calculate_volatility,
            'moving_average': self._calculate_moving_average,
            'price_trend': self._analyze_price_trend,
            'peak_analysis': self._analyze_peak_hours,
            'correlation': self._calculate_correlation,
            'statistics': self._calculate_basic_statistics,
            'forecast': self._simple_forecast
        }
    
    def analyze(self, data: pd.DataFrame, analysis_type: str, 
                parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform analysis on market data
        
        Args:
            data: DataFrame with market data
            analysis_type: Type of analysis to perform
            parameters: Additional parameters for analysis
        
        Returns:
            Dictionary with analysis results
        """
        if data is None or data.empty:
            return {'error': 'No data available for analysis'}
        
        parameters = parameters or {}
        
        try:
            if analysis_type in self.analysis_functions:
                result = self.analysis_functions[analysis_type](data, parameters)
                result['data_points'] = len(data)
                result['date_range'] = {
                    'start': data['timestamp'].min().isoformat() if 'timestamp' in data.columns else None,
                    'end': data['timestamp'].max().isoformat() if 'timestamp' in data.columns else None
                }
                return result
            else:
                # Default to comprehensive analysis
                return self._comprehensive_analysis(data, parameters)
                
        except Exception as e:
            logger.error(f"Error in analysis: {str(e)}")
            return {'error': f'Analysis failed: {str(e)}'}
    
    def _calculate_rmse(self, data: pd.DataFrame, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Root Mean Square Error against a baseline"""
        if 'price' not in data.columns:
            return {'error': 'Price column not found in data'}
        
        prices = data['price'].dropna()
        
        # Use moving average as baseline if not specified
        window = parameters.get('window', 24)  # 24-hour window
        baseline = prices.rolling(window=window, min_periods=1).mean()
        
        # Calculate RMSE
        rmse = np.sqrt(mean_squared_error(prices[window:], baseline[window:]))
        
        # Calculate additional metrics
        mae = mean_absolute_error(prices[window:], baseline[window:])
        mape = np.mean(np.abs((prices[window:] - baseline[window:]) / prices[window:])) * 100
        
        return {
            'rmse': round(rmse, 3),
            'mae': round(mae, 3),
            'mape': round(mape, 2),
            'baseline_type': f'{window}-hour moving average',
            'analysis_period': f'{len(prices) - window} hours',
            'unit': 'USD/MWh'
        }
    
    def _calculate_mae(self, data: pd.DataFrame, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Mean Absolute Error"""
        if 'price' not in data.columns:
            return {'error': 'Price column not found in data'}
        
        prices = data['price'].dropna()
        window = parameters.get('window', 24)
        baseline = prices.rolling(window=window, min_periods=1).mean()
        
        mae = mean_absolute_error(prices[window:], baseline[window:])
        
        return {
            'mae': round(mae, 3),
            'baseline_type': f'{window}-hour moving average',
            'unit': 'USD/MWh'
        }
    
    def _calculate_volatility(self, data: pd.DataFrame, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate price volatility metrics"""
        if 'price' not in data.columns:
            return {'error': 'Price column not found in data'}
        
        prices = data['price'].dropna()
        
        # Calculate returns
        returns = prices.pct_change().dropna()
        
        # Volatility metrics
        daily_vol = returns.std()
        annualized_vol = daily_vol * np.sqrt(365 * 24)  # Assuming hourly data
        
        # Price range metrics
        price_range = prices.max() - prices.min()
        coefficient_of_variation = prices.std() / prices.mean()
        
        return {
            'daily_volatility': round(daily_vol * 100, 3),
            'annualized_volatility': round(annualized_vol * 100, 2),
            'price_range': round(price_range, 2),
            'coefficient_of_variation': round(coefficient_of_variation, 3),
            'max_price': round(prices.max(), 2),
            'min_price': round(prices.min(), 2),
            'unit': 'USD/MWh'
        }
    
    def _calculate_moving_average(self, data: pd.DataFrame, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate moving averages"""
        if 'price' not in data.columns:
            return {'error': 'Price column not found in data'}
        
        prices = data['price'].dropna()
        window = parameters.get('window', 24)
        
        ma = prices.rolling(window=window).mean()
        current_ma = ma.iloc[-1] if not ma.empty else None
        
        # Calculate trend
        recent_ma = ma.tail(window//2).mean()
        earlier_ma = ma.head(window//2).mean()
        trend = 'increasing' if recent_ma > earlier_ma else 'decreasing'
        
        return {
            'moving_average': round(current_ma, 2) if current_ma else None,
            'window_hours': window,
            'trend': trend,
            'trend_magnitude': round(abs(recent_ma - earlier_ma), 2) if recent_ma and earlier_ma else None,
            'unit': 'USD/MWh'
        }
    
    def _analyze_price_trend(self, data: pd.DataFrame, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze price trends using linear regression"""
        if 'price' not in data.columns or 'timestamp' not in data.columns:
            return {'error': 'Required columns not found in data'}
        
        # Prepare data for regression
        data_clean = data.dropna(subset=['price', 'timestamp'])
        if len(data_clean) < 2:
            return {'error': 'Insufficient data for trend analysis'}
        
        # Convert timestamps to numeric values
        timestamps = pd.to_numeric(data_clean['timestamp']) / 10**9  # Convert to seconds
        prices = data_clean['price'].values
        
        # Fit linear regression
        X = timestamps.values.reshape(-1, 1)
        y = prices
        
        model = LinearRegression()
        model.fit(X, y)
        
        # Calculate trend metrics
        slope = model.coef_[0]
        r_squared = model.score(X, y)
        
        # Convert slope to $/MWh per hour
        slope_per_hour = slope * 3600
        
        trend_direction = 'increasing' if slope > 0 else 'decreasing'
        trend_strength = 'strong' if r_squared > 0.7 else 'moderate' if r_squared > 0.3 else 'weak'
        
        return {
            'trend_direction': trend_direction,
            'trend_strength': trend_strength,
            'slope_per_hour': round(slope_per_hour, 4),
            'r_squared': round(r_squared, 3),
            'price_change_per_day': round(slope_per_hour * 24, 2),
            'unit': 'USD/MWh'
        }
    
    def _analyze_peak_hours(self, data: pd.DataFrame, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze peak and off-peak hour patterns"""
        if 'price' not in data.columns or 'timestamp' not in data.columns:
            return {'error': 'Required columns not found in data'}
        
        data_clean = data.dropna(subset=['price', 'timestamp'])
        data_clean['hour'] = data_clean['timestamp'].dt.hour
        
        # Define peak hours (typically 7 AM to 10 PM)
        peak_hours = list(range(7, 23))
        
        peak_data = data_clean[data_clean['hour'].isin(peak_hours)]
        off_peak_data = data_clean[~data_clean['hour'].isin(peak_hours)]
        
        if peak_data.empty or off_peak_data.empty:
            return {'error': 'Insufficient data for peak analysis'}
        
        peak_avg = peak_data['price'].mean()
        off_peak_avg = off_peak_data['price'].mean()
        peak_premium = peak_avg - off_peak_avg
        peak_premium_pct = (peak_premium / off_peak_avg) * 100
        
        # Find highest and lowest price hours
        hourly_avg = data_clean.groupby('hour')['price'].mean()
        highest_hour = hourly_avg.idxmax()
        lowest_hour = hourly_avg.idxmin()
        
        return {
            'peak_average_price': round(peak_avg, 2),
            'off_peak_average_price': round(off_peak_avg, 2),
            'peak_premium': round(peak_premium, 2),
            'peak_premium_percentage': round(peak_premium_pct, 1),
            'highest_price_hour': int(highest_hour),
            'lowest_price_hour': int(lowest_hour),
            'peak_hours_definition': '7 AM - 10 PM',
            'unit': 'USD/MWh'
        }
    
    def _calculate_correlation(self, data: pd.DataFrame, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate correlations between different metrics"""
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        if len(numeric_columns) < 2:
            return {'error': 'Insufficient numeric columns for correlation analysis'}
        
        correlation_matrix = data[numeric_columns].corr()
        
        # Find strongest correlations (excluding self-correlations)
        correlations = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                col1 = correlation_matrix.columns[i]
                col2 = correlation_matrix.columns[j]
                corr_value = correlation_matrix.iloc[i, j]
                
                if not np.isnan(corr_value):
                    correlations.append({
                        'variables': f"{col1} vs {col2}",
                        'correlation': round(corr_value, 3)
                    })
        
        # Sort by absolute correlation value
        correlations.sort(key=lambda x: abs(x['correlation']), reverse=True)
        
        return {
            'correlations': correlations[:5],  # Top 5 correlations
            'correlation_matrix': correlation_matrix.round(3).to_dict()
        }
    
    def _calculate_basic_statistics(self, data: pd.DataFrame, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate basic statistical measures"""
        if 'price' not in data.columns:
            return {'error': 'Price column not found in data'}
        
        prices = data['price'].dropna()
        
        if prices.empty:
            return {'error': 'No valid price data found'}
        
        stats = {
            'count': len(prices),
            'mean': round(prices.mean(), 2),
            'median': round(prices.median(), 2),
            'std_dev': round(prices.std(), 2),
            'min': round(prices.min(), 2),
            'max': round(prices.max(), 2),
            'q25': round(prices.quantile(0.25), 2),
            'q75': round(prices.quantile(0.75), 2),
            'skewness': round(prices.skew(), 3),
            'kurtosis': round(prices.kurtosis(), 3),
            'unit': 'USD/MWh'
        }
        
        return stats
    
    def _simple_forecast(self, data: pd.DataFrame, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Simple price forecasting using moving average"""
        if 'price' not in data.columns:
            return {'error': 'Price column not found in data'}
        
        prices = data['price'].dropna()
        forecast_hours = parameters.get('forecast_hours', 24)
        window = parameters.get('window', 24)
        
        if len(prices) < window:
            return {'error': f'Insufficient data for forecasting (need at least {window} points)'}
        
        # Simple moving average forecast
        recent_avg = prices.tail(window).mean()
        trend = (prices.tail(window).mean() - prices.head(window).mean()) / len(prices)
        
        forecasts = []
        for i in range(1, forecast_hours + 1):
            forecast_price = recent_avg + (trend * i)
            forecasts.append(round(max(forecast_price, 0), 2))  # Ensure non-negative
        
        return {
            'forecast_method': f'{window}-hour moving average with trend',
            'forecast_hours': forecast_hours,
            'forecasted_prices': forecasts,
            'base_price': round(recent_avg, 2),
            'trend_per_hour': round(trend, 4),
            'unit': 'USD/MWh'
        }
    
    def _comprehensive_analysis(self, data: pd.DataFrame, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive analysis combining multiple metrics"""
        results = {}
        
        # Run all available analyses
        for analysis_type, func in self.analysis_functions.items():
            try:
                result = func(data, parameters)
                if 'error' not in result:
                    results[analysis_type] = result
            except Exception as e:
                logger.warning(f"Failed to run {analysis_type}: {str(e)}")
        
        return results
