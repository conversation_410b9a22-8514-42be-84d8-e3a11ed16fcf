import re
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class NLPProcessor:
    """Service for processing natural language queries about market data"""
    
    def __init__(self):
        # ISO patterns
        self.iso_patterns = {
            'ercot': r'\b(ercot|electric reliability council of texas)\b',
            'pjm': r'\b(pjm|pennsylvania new jersey maryland)\b',
            'caiso': r'\b(caiso|california iso|california independent system operator)\b',
            'nyiso': r'\b(nyiso|new york iso|new york independent system operator)\b',
            'iso-ne': r'\b(iso-ne|isone|iso new england)\b',
            'miso': r'\b(miso|midcontinent iso|midcontinent independent system operator)\b',
            'spp': r'\b(spp|southwest power pool)\b'
        }
        
        # Hub patterns
        self.hub_patterns = {
            'north': r'\b(north|northern)\s*(hub|zone)?\b',
            'south': r'\b(south|southern)\s*(hub|zone)?\b',
            'west': r'\b(west|western)\s*(hub|zone)?\b',
            'east': r'\b(east|eastern)\s*(hub|zone)?\b',
            'houston': r'\b(houston)\s*(hub|zone)?\b',
            'ad': r'\b(ad|atsi|dominion)\s*(hub|zone)?\b',
            'sp15': r'\b(sp15|sp-15)\b',
            'np15': r'\b(np15|np-15)\b',
            'zp26': r'\b(zp26|zp-26)\b'
        }
        
        # Analysis type patterns
        self.analysis_patterns = {
            'rmse': r'\b(rmse|root mean square error|root mean squared error)\b',
            'mae': r'\b(mae|mean absolute error)\b',
            'volatility': r'\b(volatility|volatile|variation|variability)\b',
            'moving_average': r'\b(moving average|ma|average|mean)\b',
            'price_trend': r'\b(trend|trending|direction|slope|increasing|decreasing)\b',
            'peak_analysis': r'\b(peak|off-peak|peak hours|peak analysis)\b',
            'correlation': r'\b(correlation|correlated|relationship)\b',
            'statistics': r'\b(statistics|stats|statistical|summary)\b',
            'forecast': r'\b(forecast|predict|prediction|future|next)\b'
        }

        # Market data type patterns (for Genscape API)
        self.data_type_patterns = {
            'lmp': r'\b(lmp|locational marginal price|marginal price|price)\b',
            'nddam': r'\b(nddam|next day day ahead|next-day day-ahead)\b',
            'da': r'\b(da|day ahead|day-ahead)\b',
            'rt': r'\b(rt|real time|real-time|realtime)\b',
            'load': r'\b(load|demand)\b',
            'generation': r'\b(generation|gen|supply)\b',
            'congestion': r'\b(congestion|cong)\b',
            'loss': r'\b(loss|losses)\b'
        }
        
        # Time period patterns
        self.time_patterns = {
            'today': r'\b(today|this day)\b',
            'yesterday': r'\b(yesterday|last day)\b',
            'this_week': r'\b(this week|current week)\b',
            'last_week': r'\b(last week|previous week|past week)\b',
            'this_month': r'\b(this month|current month)\b',
            'last_month': r'\b(last month|previous month|past month)\b',
            'this_year': r'\b(this year|current year)\b',
            'last_year': r'\b(last year|previous year|past year)\b'
        }
        
        # Specific date patterns
        self.date_patterns = [
            r'\b(\d{4}-\d{2}-\d{2})\b',  # YYYY-MM-DD
            r'\b(\d{1,2}/\d{1,2}/\d{4})\b',  # MM/DD/YYYY
            r'\b(\d{1,2}-\d{1,2}-\d{4})\b',  # MM-DD-YYYY
        ]
    
    def parse_query(self, query: str) -> Optional[Dict[str, Any]]:
        """
        Parse natural language query and extract relevant parameters
        
        Args:
            query: Natural language query string
        
        Returns:
            Dictionary with extracted parameters or None if parsing fails
        """
        try:
            query_lower = query.lower()
            
            # Extract ISO
            iso = self._extract_iso(query_lower)
            if not iso:
                logger.warning("No ISO found in query")
                return None
            
            # Extract hub
            hub = self._extract_hub(query_lower)
            
            # Extract data type (what kind of market data)
            data_type = self._extract_data_type(query_lower)

            # Extract analysis type
            analysis_type = self._extract_analysis_type(query_lower)

            # Extract time period
            start_date, end_date = self._extract_time_period(query_lower)

            # Extract additional parameters
            parameters = self._extract_parameters(query_lower)

            result = {
                'iso': iso,
                'hub': hub,
                'data_type': data_type,
                'analysis_type': analysis_type,
                'start_date': start_date,
                'end_date': end_date,
                'parameters': parameters,
                'original_query': query
            }
            
            logger.info(f"Parsed query: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error parsing query: {str(e)}")
            return None
    
    def _extract_iso(self, query: str) -> Optional[str]:
        """Extract ISO name from query"""
        for iso, pattern in self.iso_patterns.items():
            if re.search(pattern, query, re.IGNORECASE):
                return iso
        return None
    
    def _extract_hub(self, query: str) -> Optional[str]:
        """Extract hub name from query"""
        for hub, pattern in self.hub_patterns.items():
            if re.search(pattern, query, re.IGNORECASE):
                return hub
        return None

    def _extract_data_type(self, query: str) -> str:
        """Extract data type from query (LMP, NDDAM, load, etc.)"""
        for data_type, pattern in self.data_type_patterns.items():
            if re.search(pattern, query, re.IGNORECASE):
                return data_type

        # Default to LMP if no specific data type found
        return 'lmp'
    
    def _extract_analysis_type(self, query: str) -> str:
        """Extract analysis type from query"""
        # Check for specific analysis types
        for analysis_type, pattern in self.analysis_patterns.items():
            if re.search(pattern, query, re.IGNORECASE):
                return analysis_type
        
        # Default analysis based on keywords
        if any(word in query for word in ['compare', 'comparison', 'vs', 'versus']):
            return 'correlation'
        elif any(word in query for word in ['summary', 'overview', 'analysis']):
            return 'statistics'
        else:
            return 'statistics'  # Default to basic statistics
    
    def _extract_time_period(self, query: str) -> tuple[Optional[str], Optional[str]]:
        """Extract time period from query"""
        today = datetime.now()
        
        # Check for relative time periods
        for period, pattern in self.time_patterns.items():
            if re.search(pattern, query, re.IGNORECASE):
                return self._get_date_range_for_period(period, today)
        
        # Check for specific dates
        dates = []
        for pattern in self.date_patterns:
            matches = re.findall(pattern, query)
            dates.extend(matches)
        
        if len(dates) >= 2:
            return dates[0], dates[1]
        elif len(dates) == 1:
            # Single date - use as end date, start from a week before
            end_date = dates[0]
            start_date = (datetime.strptime(end_date, '%Y-%m-%d') - timedelta(days=7)).strftime('%Y-%m-%d')
            return start_date, end_date
        
        # Default to last week if no time period specified
        return self._get_date_range_for_period('last_week', today)
    
    def _get_date_range_for_period(self, period: str, reference_date: datetime) -> tuple[str, str]:
        """Get start and end dates for a named time period"""
        if period == 'today':
            start = reference_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end = reference_date
        elif period == 'yesterday':
            start = (reference_date - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(hours=23, minutes=59)
        elif period == 'this_week':
            days_since_monday = reference_date.weekday()
            start = (reference_date - timedelta(days=days_since_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
            end = reference_date
        elif period == 'last_week':
            days_since_monday = reference_date.weekday()
            this_monday = reference_date - timedelta(days=days_since_monday)
            start = (this_monday - timedelta(days=7)).replace(hour=0, minute=0, second=0, microsecond=0)
            end = (this_monday - timedelta(seconds=1))
        elif period == 'this_month':
            start = reference_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            end = reference_date
        elif period == 'last_month':
            first_this_month = reference_date.replace(day=1)
            end = first_this_month - timedelta(seconds=1)
            start = end.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        elif period == 'this_year':
            start = reference_date.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            end = reference_date
        elif period == 'last_year':
            start = reference_date.replace(year=reference_date.year-1, month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            end = reference_date.replace(year=reference_date.year-1, month=12, day=31, hour=23, minute=59, second=59)
        else:
            # Default to last 7 days
            start = reference_date - timedelta(days=7)
            end = reference_date
        
        return start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')
    
    def _extract_parameters(self, query: str) -> Dict[str, Any]:
        """Extract additional parameters from query"""
        parameters = {}
        
        # Extract window size for moving averages
        window_match = re.search(r'(\d+)\s*(hour|day|week)', query)
        if window_match:
            number = int(window_match.group(1))
            unit = window_match.group(2)
            
            if unit == 'hour':
                parameters['window'] = number
            elif unit == 'day':
                parameters['window'] = number * 24
            elif unit == 'week':
                parameters['window'] = number * 24 * 7
        
        # Extract forecast horizon
        forecast_match = re.search(r'(next|forecast)\s*(\d+)\s*(hour|day)', query)
        if forecast_match:
            number = int(forecast_match.group(2))
            unit = forecast_match.group(3)
            
            if unit == 'hour':
                parameters['forecast_hours'] = number
            elif unit == 'day':
                parameters['forecast_hours'] = number * 24
        
        return parameters
    
    def get_query_intent(self, query: str) -> str:
        """Determine the intent of the query"""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['what is', 'tell me', 'show me']):
            return 'information_request'
        elif any(word in query_lower for word in ['compare', 'difference', 'vs']):
            return 'comparison'
        elif any(word in query_lower for word in ['predict', 'forecast', 'future']):
            return 'prediction'
        elif any(word in query_lower for word in ['analyze', 'analysis', 'calculate']):
            return 'analysis'
        else:
            return 'general_query'
    
    def extract_entities(self, query: str) -> Dict[str, List[str]]:
        """Extract all entities from the query"""
        entities = {
            'isos': [],
            'hubs': [],
            'metrics': [],
            'time_periods': []
        }
        
        query_lower = query.lower()
        
        # Extract ISOs
        for iso, pattern in self.iso_patterns.items():
            if re.search(pattern, query_lower):
                entities['isos'].append(iso)
        
        # Extract hubs
        for hub, pattern in self.hub_patterns.items():
            if re.search(pattern, query_lower):
                entities['hubs'].append(hub)
        
        # Extract metrics
        for metric, pattern in self.analysis_patterns.items():
            if re.search(pattern, query_lower):
                entities['metrics'].append(metric)
        
        # Extract time periods
        for period, pattern in self.time_patterns.items():
            if re.search(pattern, query_lower):
                entities['time_periods'].append(period)
        
        return entities
