# Market Price Analysis Chatbot

A sophisticated natural language chatbot for analyzing electricity market prices across different ISOs (Independent System Operators). The chatbot integrates with Genscape APIs to fetch real market data, perform statistical analysis, and generate natural language responses with scientific insights.

## Genscape API Integration

This chatbot is specifically designed to work with two Genscape APIs:

### 1. Market Price API
- **Endpoint**: `/v1/getepcalcsiddata?sids={sids}&start_date={start_date}&end_date={end_date}&interval={interval}`
- **Purpose**: Fetch time series market data using sensor IDs
- **Response**: SensorID, Value, date columns

### 2. Metadata API
- **Endpoint**: `/v1/searchreportmetadata[?region][&reportId][&limit][&offset]`
- **Purpose**: Search and discover available sensors and their descriptions
- **Response**: reportId, reportName, reportRegion, sids[].id, sids[].description

The chatbot automatically:
1. Searches metadata to find relevant sensor IDs based on natural language queries
2. Fetches actual market data using the identified sensor IDs
3. Performs analysis and generates insights

## Features

### 🤖 Natural Language Processing
- Parse complex queries about market prices and analysis
- Extract entities: ISOs, hubs, time periods, analysis types
- Support for multiple query intents (information requests, comparisons, predictions)

### 📊 Advanced Analytics
- **RMSE & MAE Calculations**: Root Mean Square Error and Mean Absolute Error analysis
- **Volatility Analysis**: Daily and annualized volatility metrics
- **Price Trend Analysis**: Linear regression-based trend detection
- **Peak Hours Analysis**: Peak vs off-peak pricing patterns
- **Statistical Summaries**: Comprehensive descriptive statistics
- **Price Forecasting**: Simple moving average-based predictions

### 🏢 Supported ISOs
- **ERCOT** (Electric Reliability Council of Texas)
- **PJM** (Pennsylvania, New Jersey, Maryland)
- **CAISO** (California Independent System Operator)
- **NYISO** (New York Independent System Operator)
- **ISO-NE** (ISO New England)
- **MISO** (Midcontinent Independent System Operator)
- **SPP** (Southwest Power Pool)

### 💬 Interactive Chat Interface
- Real-time messaging with typing indicators
- Example queries for quick start
- Scientific metrics display with visual cards
- Data summary information
- Responsive design with modern UI

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Setup Instructions

1. **Clone or download the project**
   ```bash
   cd market-price-chatbot
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your API configuration:
   ```
   MARKET_API_BASE_URL=http://your-internal-api.com/api
   MARKET_API_KEY=your_api_key_here
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Access the chatbot**
   Open your browser and navigate to `http://localhost:5000`

## Usage Examples

### Basic Queries
```
"NDDAM price of ERCOT this week"
"What is the RMSE for ERCOT LMP North Hub?"
"Show me volatility for PJM day-ahead prices"
"Analyze CAISO load trends last month"
"Real-time LMP statistics for NYISO"
```

### Advanced Queries
```
"Calculate the 24-hour moving average RMSE for ERCOT NDDAM"
"Compare peak vs off-peak pricing for NYISO LMP this month"
"Forecast PJM real-time prices for the next 12 hours"
"What are the statistics for MISO load last week?"
"Volatility analysis for CAISO generation data"
```

### Supported Data Types
- **LMP** (Locational Marginal Prices)
- **NDDAM** (Next Day Day-Ahead Market)
- **DA** (Day-Ahead prices)
- **RT** (Real-Time prices)
- **Load/Demand** data
- **Generation** data
- **Congestion** data
- **Loss** data

### Time Period Specifications
- **Relative**: "this week", "last month", "yesterday"
- **Specific dates**: "2024-01-15", "01/15/2024"
- **Ranges**: "from 2024-01-01 to 2024-01-07"

## API Integration

### Genscape API Configuration

The chatbot requires access to Genscape's Market Intelligence APIs:

#### Market Price API
```
GET https://api.genscape.com/marketintelligence/na/v1/getepcalcsiddata
Parameters:
- sids: Sensor ID(s) (comma-separated)
- start_date: Start date (YYYY-MM-DD)
- end_date: End date (YYYY-MM-DD)
- interval: Data interval (hourly, daily, etc.)

Response:
[
  {
    "SensorID": "12345",
    "Value": 45.67,
    "date": "2024-01-01T00:00:00Z"
  }
]
```

#### Metadata Search API
```
GET https://api.genscape.com/marketintelligence/na/v1/searchreportmetadata
Parameters:
- region: Geographic region (optional)
- reportId: Specific report ID (optional)
- limit: Maximum results (optional)
- offset: Pagination offset (optional)

Response:
{
  "reports": [
    {
      "reportId": "123",
      "reportName": "ERCOT LMP Report",
      "reportRegion": "ERCOT",
      "sids": [
        {
          "id": "12345",
          "description": "ERCOT North Hub LMP"
        }
      ]
    }
  ]
}
```

### Intelligent Sensor Discovery

The chatbot automatically:
1. **Parses natural language** to extract ISO, data type, and location
2. **Searches metadata** to find relevant sensors using keyword matching
3. **Scores and ranks** sensors based on relevance to the query
4. **Fetches data** using the best matching sensor ID

### Mock Data Mode

If Genscape APIs are not available, the chatbot automatically generates realistic mock data for testing and demonstration purposes.

## Testing

Run the test suite to verify functionality:

```bash
# Run all tests
python -m pytest tests/

# Run specific test files
python -m unittest tests.test_nlp_service
python -m unittest tests.test_analysis_service

# Run with verbose output
python -m unittest tests.test_nlp_service -v
```

## Architecture

### Core Components

1. **Flask Application** (`app.py`)
   - Main web server and API endpoints
   - Request routing and error handling

2. **API Service** (`services/api_service.py`)
   - Integration with internal market data API
   - Data fetching and normalization
   - Mock data generation for testing

3. **NLP Processor** (`services/nlp_service.py`)
   - Natural language query parsing
   - Entity extraction (ISOs, hubs, time periods)
   - Intent classification

4. **Data Analyzer** (`services/analysis_service.py`)
   - Statistical analysis functions
   - RMSE, volatility, trend calculations
   - Forecasting algorithms

5. **Response Generator** (`services/response_generator.py`)
   - Natural language response generation
   - Scientific formatting and insights
   - Metrics visualization

6. **Chat Interface** (`templates/index.html`)
   - Modern web-based chat UI
   - Real-time messaging
   - Results visualization

### Data Flow

1. User enters natural language query
2. NLP service parses query and extracts parameters
3. API service fetches relevant market data
4. Analysis service performs requested calculations
5. Response generator creates natural language response
6. Results displayed in chat interface with metrics

## Configuration

### Environment Variables

- `MARKET_API_BASE_URL`: Base URL for your internal API
- `MARKET_API_KEY`: API authentication key
- `FLASK_ENV`: Flask environment (development/production)
- `FLASK_DEBUG`: Enable debug mode (True/False)
- `LOG_LEVEL`: Logging level (DEBUG/INFO/WARNING/ERROR)

### Customization

#### Adding New ISOs
Edit `services/api_service.py` and add to `iso_mappings`:
```python
self.iso_mappings = {
    'your_iso': 'YOUR_ISO_NAME',
    # ... existing mappings
}
```

#### Adding New Analysis Types
1. Add analysis function to `services/analysis_service.py`
2. Add response template to `services/response_generator.py`
3. Add pattern recognition to `services/nlp_service.py`

## Troubleshooting

### Common Issues

1. **API Connection Errors**
   - Verify `MARKET_API_BASE_URL` and `MARKET_API_KEY` in `.env`
   - Check network connectivity to internal API
   - Review API authentication requirements

2. **No Data Returned**
   - Verify ISO and hub names are correct
   - Check date ranges are valid
   - Ensure API has data for requested period

3. **Analysis Errors**
   - Verify data contains required columns (`timestamp`, `price`)
   - Check for sufficient data points for analysis
   - Review data quality and missing values

### Debug Mode

Enable debug mode for detailed error information:
```bash
export FLASK_DEBUG=True
python app.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions or issues:
1. Check the troubleshooting section
2. Review the test cases for usage examples
3. Create an issue with detailed error information
