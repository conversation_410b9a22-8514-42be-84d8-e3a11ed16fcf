# AI Training Assistant - Market Analysis & Roleplay Training

A comprehensive AI-powered training platform that combines sophisticated market price analysis with interactive roleplay training for client-facing teams. The system features natural language processing for market data analysis and realistic business meeting simulations to improve presentation and communication skills.

## Genscape API Integration

This chatbot is specifically designed to work with two Genscape APIs:

### 1. Market Price API
- **Endpoint**: `/v1/getepcalcsiddata?sids={sids}&start_date={start_date}&end_date={end_date}&interval={interval}`
- **Purpose**: Fetch time series market data using sensor IDs
- **Response**: SensorID, Value, date columns

### 2. Metadata API
- **Endpoint**: `/v1/searchreportmetadata[?region][&reportId][&limit][&offset]`
- **Purpose**: Search and discover available sensors and their descriptions
- **Response**: reportId, reportName, reportRegion, sids[].id, sids[].description

The chatbot automatically:
1. Searches metadata to find relevant sensor IDs based on natural language queries
2. Fetches actual market data using the identified sensor IDs
3. Performs analysis and generates insights

## Features

### 🎭 AI-Powered Roleplay Training
- **Multi-Layered Personas**: Client, Vendor, and Consultant roles with realistic characteristics
- **Dynamic Mood Simulation**: Curious, Skeptical, Indifferent, Passive, and Not Interested behaviors
- **Contextual Scenarios**: Industry-specific backgrounds (Energy, Technology, Financial Services)
- **Real-time Feedback**: Immediate evaluation with strengths, improvements, and suggestions
- **Adaptive Questioning**: AI-generated questions that respond to user performance
- **Comprehensive Evaluation**: Final scoring with confidence levels and recommendations

### 🤖 Natural Language Processing
- Parse complex queries about market prices and analysis
- Extract entities: ISOs, hubs, time periods, analysis types
- Support for multiple query intents (information requests, comparisons, predictions)
- Gemini AI integration for intelligent natural language understanding

### 📊 Advanced Analytics
- **RMSE & MAE Calculations**: Root Mean Square Error and Mean Absolute Error analysis
- **Volatility Analysis**: Daily and annualized volatility metrics
- **Price Trend Analysis**: Linear regression-based trend detection
- **Peak Hours Analysis**: Peak vs off-peak pricing patterns
- **Statistical Summaries**: Comprehensive descriptive statistics
- **Price Forecasting**: Simple moving average-based predictions

### 🏢 Supported ISOs
- **ERCOT** (Electric Reliability Council of Texas)
- **PJM** (Pennsylvania, New Jersey, Maryland)
- **CAISO** (California Independent System Operator)
- **NYISO** (New York Independent System Operator)
- **ISO-NE** (ISO New England)
- **MISO** (Midcontinent Independent System Operator)
- **SPP** (Southwest Power Pool)

### 💬 Dual-Mode Interface
- **Market Analysis Mode**: Traditional chatbot for market data queries
- **Roleplay Training Mode**: Interactive training sessions with AI personas
- Real-time messaging with typing indicators
- Example queries and scenarios for quick start
- Scientific metrics display with visual cards
- Comprehensive feedback and evaluation displays
- Responsive design with modern UI

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Setup Instructions

1. **Clone or download the project**
   ```bash
   cd market-price-chatbot
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your API configuration:
   ```
   MARKET_API_BASE_URL=http://your-internal-api.com/api
   MARKET_API_KEY=your_api_key_here
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Access the chatbot**
   Open your browser and navigate to `http://localhost:5000`

## Usage Examples

### Market Analysis Mode

#### Basic Queries
```
"NDDAM price of ERCOT this week"
"What is the RMSE for ERCOT LMP North Hub?"
"Show me volatility for PJM day-ahead prices"
"Analyze CAISO load trends last month"
"Real-time LMP statistics for NYISO"
```

#### Advanced Queries
```
"Calculate the 24-hour moving average RMSE for ERCOT NDDAM"
"Compare peak vs off-peak pricing for NYISO LMP this month"
"Forecast PJM real-time prices for the next 12 hours"
"What are the statistics for MISO load last week?"
"Volatility analysis for CAISO generation data"
```

### Roleplay Training Mode

#### Example Training Scenarios
```
Persona: Client | Mood: Skeptical | Agenda: "Energy Transition with Tesla"
→ Practice handling challenging questions about renewable energy ROI

Persona: Vendor | Mood: Curious | Agenda: "Digital Transformation Strategy"
→ Learn to engage interested prospects seeking detailed information

Persona: Consultant | Mood: Indifferent | Agenda: "Risk Management Solutions"
→ Develop skills for presenting to neutral, fact-focused audiences
```

#### Sample Roleplay Questions
```
Client (Skeptical): "I've heard similar promises before. How can you prove
this won't be another failed initiative?"

Vendor (Curious): "Can you walk me through how this integrates with our
existing technology stack?"

Consultant (Indifferent): "What's the bottom line cost and when will we see ROI?"
```

### Supported Data Types
- **LMP** (Locational Marginal Prices)
- **NDDAM** (Next Day Day-Ahead Market)
- **DA** (Day-Ahead prices)
- **RT** (Real-Time prices)
- **Load/Demand** data
- **Generation** data
- **Congestion** data
- **Loss** data

### Time Period Specifications
- **Relative**: "this week", "last month", "yesterday"
- **Specific dates**: "2024-01-15", "01/15/2024"
- **Ranges**: "from 2024-01-01 to 2024-01-07"

## Roleplay Training System

### Overview
The roleplay training system uses advanced AI to simulate realistic business meetings, helping client-facing teams practice and improve their presentation skills. The system provides immediate feedback and adapts to user performance.

### Persona Types

#### Client Personas
- **Focus**: Business value, ROI, risk mitigation
- **Decision Style**: Results-oriented, risk-conscious
- **Key Concerns**: Financial impact, implementation risks, competitive advantage
- **Communication Style**: Direct, executive-level, time-conscious

#### Vendor Personas
- **Focus**: Solution fit, partnership value, technical capabilities
- **Decision Style**: Collaborative, solution-oriented
- **Key Concerns**: Customer needs, technical integration, long-term relationships
- **Communication Style**: Consultative, customer-centric, detail-oriented

#### Consultant Personas
- **Focus**: Strategic alignment, best practices, implementation feasibility
- **Decision Style**: Analytical, framework-driven
- **Key Concerns**: Strategic implications, change management, measurable outcomes
- **Communication Style**: Strategic, data-driven, process-focused

### Mood Variations

#### Curious (High Engagement)
- **Behavior**: Asks detailed follow-up questions, seeks examples
- **Response Style**: "Tell me more about...", "How does this work with..."
- **Training Focus**: Handling engaged prospects, providing comprehensive information

#### Skeptical (High Challenge)
- **Behavior**: Questions assumptions, demands evidence, highlights risks
- **Response Style**: "How can you prove...", "What about the risks of..."
- **Training Focus**: Overcoming objections, providing proof points

#### Indifferent (Neutral)
- **Behavior**: Focuses on facts, asks for bottom-line information
- **Response Style**: "What are the facts?", "Show me the numbers"
- **Training Focus**: Engaging neutral audiences, compelling value propositions

#### Passive (Low Engagement)
- **Behavior**: Minimal participation, short responses, waits for information
- **Response Style**: "Okay", "I see", "What else?"
- **Training Focus**: Encouraging participation, interactive presentation skills

#### Not Interested (Disengaged)
- **Behavior**: Shows impatience, questions necessity, seeks to end discussion
- **Response Style**: "How long will this take?", "Is this really necessary?"
- **Training Focus**: Quick value delivery, time-efficient presentations

### Evaluation Framework

The system evaluates performance across multiple dimensions:

#### Technical Knowledge (30% weight)
- Accuracy of technical information
- Depth of understanding
- Appropriate use of terminology
- Practical application examples

#### Business Acumen (35% weight)
- Business value articulation
- Market understanding
- Risk assessment capabilities
- Strategic thinking demonstration

#### Communication Skills (35% weight)
- Clarity and conciseness
- Audience adaptation
- Active listening demonstration
- Professional presence maintenance

### Training Process

1. **Setup**: Select persona type, mood, agenda, and industry context
2. **Session**: Engage in 3-5 question roleplay with AI persona
3. **Feedback**: Receive immediate evaluation after each response
4. **Adaptation**: AI adjusts difficulty and questions based on performance
5. **Evaluation**: Complete session with comprehensive scoring and recommendations

### Industry Contexts

#### Energy & Utilities
- **Trends**: Energy transition, renewable integration, grid modernization
- **Challenges**: Regulatory compliance, price volatility, infrastructure aging
- **Key Topics**: FERC regulations, ESG goals, grid reliability

#### Technology
- **Trends**: AI adoption, cloud migration, digital transformation
- **Challenges**: Cybersecurity, legacy integration, talent acquisition
- **Key Topics**: Scalability, data privacy, technical architecture

#### Financial Services
- **Trends**: Digital banking, fintech disruption, ESG investing
- **Challenges**: Regulatory compliance, cybersecurity, market volatility
- **Key Topics**: Basel III, risk management, customer experience

## API Integration

### Roleplay Training API

#### Create Roleplay Session
```
POST /api/roleplay/create
Content-Type: application/json

{
    "persona_type": "client|vendor|consultant",
    "mood_type": "curious|skeptical|indifferent|passive|not_interested",
    "agenda": "Meeting agenda description",
    "industry": "energy|technology|financial",
    "max_questions": 5
}

Response:
{
    "session_id": "uuid",
    "persona_profile": {...},
    "mood_profile": {...},
    "dynamic_persona": {...},
    "context": {...},
    "first_question": {...},
    "coaching_tips": [...],
    "status": "created"
}
```

#### Submit Response
```
POST /api/roleplay/{session_id}/respond
Content-Type: application/json

{
    "response": "User's response to the question",
    "question_text": "The question that was asked",
    "question_type": "technical_knowledge|business_acumen|communication_skills",
    "expected_topics": ["topic1", "topic2"],
    "industry": "energy"
}

Response:
{
    "feedback": {
        "overall_score": 7.5,
        "strengths": [...],
        "improvement_areas": [...],
        "specific_suggestions": [...],
        "missed_opportunities": [...],
        "next_steps": [...]
    },
    "mood_response": {...},
    "next_question": {...},
    "session_progress": {...},
    "status": "feedback_provided"
}
```

#### Complete Session
```
POST /api/roleplay/{session_id}/complete

Response:
{
    "session_id": "uuid",
    "status": "completed",
    "evaluation": {
        "overall_score": 7.2,
        "confidence_level": "Good",
        "total_interactions": 5,
        "strengths": [...],
        "improvement_areas": [...],
        "recommendations": [...],
        "session_summary": {...}
    }
}
```

#### Get Session Status
```
GET /api/roleplay/{session_id}/status

Response:
{
    "session": {...},
    "persona_profile": {...},
    "mood_profile": {...}
}
```

### Genscape API Configuration

The chatbot requires access to Genscape's Market Intelligence APIs:

#### Market Price API
```
GET https://api.genscape.com/marketintelligence/na/v1/getepcalcsiddata
Parameters:
- sids: Sensor ID(s) (comma-separated)
- start_date: Start date (YYYY-MM-DD)
- end_date: End date (YYYY-MM-DD)
- interval: Data interval (hourly, daily, etc.)

Response:
[
  {
    "SensorID": "12345",
    "Value": 45.67,
    "date": "2024-01-01T00:00:00Z"
  }
]
```

#### Metadata Search API
```
GET https://api.genscape.com/marketintelligence/na/v1/searchreportmetadata
Parameters:
- region: Geographic region (optional)
- reportId: Specific report ID (optional)
- limit: Maximum results (optional)
- offset: Pagination offset (optional)

Response:
{
  "reports": [
    {
      "reportId": "123",
      "reportName": "ERCOT LMP Report",
      "reportRegion": "ERCOT",
      "sids": [
        {
          "id": "12345",
          "description": "ERCOT North Hub LMP"
        }
      ]
    }
  ]
}
```

### Intelligent Sensor Discovery

The chatbot automatically:
1. **Parses natural language** to extract ISO, data type, and location
2. **Searches metadata** to find relevant sensors using keyword matching
3. **Scores and ranks** sensors based on relevance to the query
4. **Fetches data** using the best matching sensor ID

### Mock Data Mode

If Genscape APIs are not available, the chatbot automatically generates realistic mock data for testing and demonstration purposes.

## Testing

### Market Analysis Testing

Run the test suite to verify market analysis functionality:

```bash
# Run all tests
python -m pytest tests/

# Run specific test files
python -m unittest tests.test_nlp_service
python -m unittest tests.test_analysis_service

# Run with verbose output
python -m unittest tests.test_nlp_service -v
```

### Roleplay System Testing

Test the complete roleplay training system:

```bash
# Run comprehensive roleplay system test
python test_roleplay_system.py

# Test specific components
python -c "
from services.roleplay_service import RoleplayService
from services.persona_manager import PersonaManager
from services.mood_engine import MoodEngine

# Test persona generation
pm = PersonaManager()
persona = pm.generate_dynamic_persona('client', 'Energy Transition', 'energy')
print('Persona generated successfully:', persona.get('persona_type'))

# Test mood simulation
me = MoodEngine()
mood_response = me.generate_mood_response('skeptical', {'agenda': 'test'}, 0.7)
print('Mood response generated:', mood_response.get('response_type'))
"
```

### Integration Testing

Test the complete system integration:

```bash
# Start the server
python app.py

# In another terminal, run integration tests
python test_roleplay_system.py

# Test web interface manually
# Navigate to http://127.0.0.1:5000
# Switch to "Roleplay Training" mode
# Create a session and test the interaction flow
```

## Project Structure

```
├── app.py                          # Main Flask application with dual-mode routing
├── requirements.txt                # Python dependencies
├── README.md                      # Project documentation
├── roleplay_examples.md           # Comprehensive roleplay examples and scenarios
├── test_roleplay_system.py        # Comprehensive test suite for roleplay system
│
├── services/                      # Core service modules
│   ├── __init__.py
│   ├── nlp_service.py            # Natural language processing for market queries
│   ├── analysis_service.py       # Market data analysis and calculations
│   ├── genscape_service.py       # Genscape API integration
│   ├── roleplay_service.py       # Core roleplay orchestration service
│   ├── persona_manager.py        # Dynamic persona generation and management
│   ├── mood_engine.py            # Mood simulation and behavioral patterns
│   ├── question_generator.py     # AI-powered question generation
│   └── feedback_generator.py     # Comprehensive feedback and evaluation
│
├── templates/                     # HTML templates
│   └── index.html                # Dual-mode web interface (Market + Roleplay)
│
├── static/                        # Static assets (if any)
│
└── tests/                         # Test files
    ├── test_nlp_service.py
    ├── test_analysis_service.py
    └── test_genscape_service.py
```

### Key Components

#### Market Analysis Components
- **NLP Service**: Parses natural language queries and extracts market entities
- **Analysis Service**: Performs statistical calculations (RMSE, volatility, trends)
- **Genscape Service**: Integrates with external market data APIs

#### Roleplay Training Components
- **Roleplay Service**: Orchestrates complete training sessions
- **Persona Manager**: Creates realistic business personas with industry context
- **Mood Engine**: Simulates different engagement styles and behavioral patterns
- **Question Generator**: Uses AI to create contextual, challenging questions
- **Feedback Generator**: Provides detailed evaluation and improvement suggestions

#### Shared Components
- **Flask App**: Unified web application with mode switching
- **Web Interface**: Responsive UI supporting both market analysis and roleplay training
- **Gemini AI Integration**: Powers both market analysis and roleplay interactions

### Data Flow

#### Market Analysis Flow
1. User enters natural language query in Market Analysis mode
2. NLP service parses query and extracts parameters
3. API service fetches relevant market data
4. Analysis service performs requested calculations
5. Response generator creates natural language response
6. Results displayed in chat interface with metrics

#### Roleplay Training Flow
1. User selects persona, mood, and agenda in Roleplay Training mode
2. Roleplay service creates session with dynamic persona and context
3. Question generator creates first contextual question
4. User responds to AI persona's questions
5. Feedback generator evaluates response and provides detailed feedback
6. Mood engine adapts persona behavior based on user performance
7. Process repeats until session completion with final evaluation

## Configuration

### Environment Variables

- `MARKET_API_BASE_URL`: Base URL for your internal API
- `MARKET_API_KEY`: API authentication key
- `FLASK_ENV`: Flask environment (development/production)
- `FLASK_DEBUG`: Enable debug mode (True/False)
- `LOG_LEVEL`: Logging level (DEBUG/INFO/WARNING/ERROR)

### Customization

#### Adding New ISOs
Edit `services/api_service.py` and add to `iso_mappings`:
```python
self.iso_mappings = {
    'your_iso': 'YOUR_ISO_NAME',
    # ... existing mappings
}
```

#### Adding New Analysis Types
1. Add analysis function to `services/analysis_service.py`
2. Add response template to `services/response_generator.py`
3. Add pattern recognition to `services/nlp_service.py`

## Troubleshooting

### Common Issues

1. **API Connection Errors**
   - Verify `MARKET_API_BASE_URL` and `MARKET_API_KEY` in `.env`
   - Check network connectivity to internal API
   - Review API authentication requirements

2. **No Data Returned**
   - Verify ISO and hub names are correct
   - Check date ranges are valid
   - Ensure API has data for requested period

3. **Analysis Errors**
   - Verify data contains required columns (`timestamp`, `price`)
   - Check for sufficient data points for analysis
   - Review data quality and missing values

### Debug Mode

Enable debug mode for detailed error information:
```bash
export FLASK_DEBUG=True
python app.py
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

#### For Market Analysis Features
- Follow existing patterns in `nlp_service.py` and `analysis_service.py`
- Add comprehensive test coverage for new analysis methods
- Ensure proper error handling for API failures

#### For Roleplay Training Features
- Maintain consistency with existing persona and mood patterns
- Add new industry contexts to `persona_manager.py`
- Test new features with the comprehensive test suite
- Update `roleplay_examples.md` with new scenarios

#### General Guidelines
- Use type hints and docstrings for all new functions
- Follow PEP 8 style guidelines
- Add appropriate error handling and logging
- Update documentation for new features

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions or issues:
1. Check the troubleshooting section
2. Review the test cases for usage examples
3. Create an issue with detailed error information
