"""
Test script for the Roleplay Training System
"""

import requests
import json
import time
from datetime import datetime

class RoleplaySystemTester:
    """Comprehensive tester for the roleplay training system"""
    
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session_id = None
        
    def test_health_check(self):
        """Test if the server is running"""
        print("🔍 Testing server health...")
        try:
            response = requests.get(f"{self.base_url}/api/health")
            if response.status_code == 200:
                print("✅ Server is healthy")
                return True
            else:
                print(f"❌ Server health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to server: {e}")
            return False
    
    def test_create_roleplay_session(self):
        """Test creating a new roleplay session"""
        print("\n🎭 Testing roleplay session creation...")
        
        test_cases = [
            {
                "name": "Client - Skeptical - Energy Transition",
                "data": {
                    "persona_type": "client",
                    "mood_type": "skeptical",
                    "agenda": "Energy Transition with Tesla",
                    "industry": "energy",
                    "max_questions": 3
                }
            },
            {
                "name": "Vendor - Curious - Digital Transformation",
                "data": {
                    "persona_type": "vendor",
                    "mood_type": "curious",
                    "agenda": "Digital Transformation Strategy",
                    "industry": "technology",
                    "max_questions": 3
                }
            },
            {
                "name": "Consultant - Indifferent - Risk Management",
                "data": {
                    "persona_type": "consultant",
                    "mood_type": "indifferent",
                    "agenda": "Risk Management Solutions",
                    "industry": "financial",
                    "max_questions": 3
                }
            }
        ]
        
        for test_case in test_cases:
            print(f"\n  Testing: {test_case['name']}")
            try:
                response = requests.post(
                    f"{self.base_url}/api/roleplay/create",
                    json=test_case['data']
                )
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"  ✅ Session created: {data['session_id']}")
                    print(f"     Persona: {data['persona_profile']['name']}")
                    print(f"     Mood: {data['mood_profile']['description']}")
                    print(f"     First Question: {data['first_question']['question_text'][:100]}...")
                    
                    # Store session ID for further testing
                    if not self.session_id:
                        self.session_id = data['session_id']
                    
                    return True
                else:
                    print(f"  ❌ Failed to create session: {response.status_code}")
                    print(f"     Error: {response.text}")
                    return False
                    
            except Exception as e:
                print(f"  ❌ Exception during session creation: {e}")
                return False
    
    def test_roleplay_interaction(self):
        """Test roleplay interaction flow"""
        if not self.session_id:
            print("❌ No session ID available for interaction testing")
            return False
            
        print(f"\n💬 Testing roleplay interaction with session: {self.session_id}")
        
        test_responses = [
            "Our energy transition strategy focuses on renewable integration and grid modernization. We're implementing a phased approach that includes solar installations, battery storage, and smart grid technologies. The expected ROI is 15% over 5 years with significant risk mitigation benefits.",
            
            "The technical architecture leverages cloud-native microservices with AI-powered analytics. We use containerized deployments for scalability and implement robust cybersecurity measures. Integration with existing systems is handled through standardized APIs.",
            
            "From a business perspective, this solution addresses three key challenges: operational efficiency, cost optimization, and regulatory compliance. We've seen similar implementations reduce operational costs by 25% while improving system reliability."
        ]
        
        for i, response in enumerate(test_responses, 1):
            print(f"\n  Response {i}: {response[:100]}...")
            
            try:
                api_response = requests.post(
                    f"{self.base_url}/api/roleplay/{self.session_id}/respond",
                    json={
                        "response": response,
                        "question_text": "Sample question for testing",
                        "question_type": "business_acumen",
                        "expected_topics": ["strategy", "implementation", "ROI"],
                        "industry": "energy"
                    }
                )
                
                if api_response.status_code == 200:
                    data = api_response.json()
                    feedback = data['feedback']
                    
                    print(f"  ✅ Feedback received:")
                    print(f"     Score: {feedback['overall_score']}/10")
                    print(f"     Strengths: {len(feedback['strengths'])} items")
                    print(f"     Improvements: {len(feedback['improvement_areas'])} items")
                    
                    if data.get('next_question'):
                        print(f"     Next Question: {data['next_question']['question_text'][:100]}...")
                    
                    if data['session_progress']['session_complete']:
                        print("  🎯 Session marked as complete")
                        break
                        
                else:
                    print(f"  ❌ Failed to process response: {api_response.status_code}")
                    print(f"     Error: {api_response.text}")
                    return False
                    
            except Exception as e:
                print(f"  ❌ Exception during interaction: {e}")
                return False
        
        return True
    
    def test_session_completion(self):
        """Test session completion and evaluation"""
        if not self.session_id:
            print("❌ No session ID available for completion testing")
            return False
            
        print(f"\n🏁 Testing session completion for: {self.session_id}")
        
        try:
            response = requests.post(f"{self.base_url}/api/roleplay/{self.session_id}/complete")
            
            if response.status_code == 200:
                data = response.json()
                evaluation = data['evaluation']
                
                print("✅ Session completed successfully")
                print(f"   Overall Score: {evaluation['overall_score']}/10")
                print(f"   Confidence Level: {evaluation['confidence_level']}")
                print(f"   Total Interactions: {evaluation['total_interactions']}")
                print(f"   Strengths: {len(evaluation['strengths'])} items")
                print(f"   Recommendations: {len(evaluation['recommendations'])} items")
                
                return True
            else:
                print(f"❌ Failed to complete session: {response.status_code}")
                print(f"   Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Exception during session completion: {e}")
            return False
    
    def test_session_status(self):
        """Test session status retrieval"""
        if not self.session_id:
            print("❌ No session ID available for status testing")
            return False
            
        print(f"\n📊 Testing session status for: {self.session_id}")
        
        try:
            response = requests.get(f"{self.base_url}/api/roleplay/{self.session_id}/status")
            
            if response.status_code == 200:
                data = response.json()
                session = data['session']
                
                print("✅ Session status retrieved")
                print(f"   Status: {session['status']}")
                print(f"   Questions: {session['current_question_count']}/{session['max_questions']}")
                print(f"   Session ID: {session['session_id']}")
                print(f"   Persona: {session['persona_type']}")
                print(f"   Mood: {session['mood_type']}")
                
                return True
            else:
                print(f"❌ Failed to get session status: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Exception during status check: {e}")
            return False
    
    def test_error_handling(self):
        """Test error handling scenarios"""
        print("\n🚨 Testing error handling...")
        
        # Test invalid persona
        print("  Testing invalid persona...")
        response = requests.post(
            f"{self.base_url}/api/roleplay/create",
            json={
                "persona_type": "invalid_persona",
                "mood_type": "curious",
                "agenda": "Test Agenda"
            }
        )
        
        if response.status_code == 400:
            print("  ✅ Invalid persona properly rejected")
        else:
            print(f"  ❌ Expected 400, got {response.status_code}")
        
        # Test missing parameters
        print("  Testing missing parameters...")
        response = requests.post(
            f"{self.base_url}/api/roleplay/create",
            json={"persona_type": "client"}
        )
        
        if response.status_code == 400:
            print("  ✅ Missing parameters properly rejected")
        else:
            print(f"  ❌ Expected 400, got {response.status_code}")
        
        # Test invalid session ID
        print("  Testing invalid session ID...")
        response = requests.get(f"{self.base_url}/api/roleplay/invalid_session_id/status")
        
        if response.status_code == 404:
            print("  ✅ Invalid session ID properly rejected")
        else:
            print(f"  ❌ Expected 404, got {response.status_code}")
        
        return True
    
    def run_comprehensive_test(self):
        """Run all tests in sequence"""
        print("🚀 Starting Comprehensive Roleplay System Test")
        print("=" * 60)
        
        start_time = datetime.now()
        
        tests = [
            ("Health Check", self.test_health_check),
            ("Session Creation", self.test_create_roleplay_session),
            ("Session Status", self.test_session_status),
            ("Roleplay Interaction", self.test_roleplay_interaction),
            ("Session Completion", self.test_session_completion),
            ("Error Handling", self.test_error_handling)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} PASSED")
                else:
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                print(f"❌ {test_name} FAILED with exception: {e}")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print("\n" + "="*60)
        print("🎯 TEST SUMMARY")
        print("="*60)
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        print(f"Duration: {duration:.2f} seconds")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Roleplay system is working correctly.")
        else:
            print("⚠️  Some tests failed. Please check the output above.")
        
        return passed == total

def main():
    """Main test execution"""
    print("🎭 Roleplay Training System - Comprehensive Test Suite")
    print("=" * 60)
    
    tester = RoleplaySystemTester()
    success = tester.run_comprehensive_test()
    
    if success:
        print("\n🌟 Ready for production! Try the web interface at http://127.0.0.1:5000")
        print("💡 Switch to 'Roleplay Training' mode and start a session.")
    else:
        print("\n🔧 Please fix the issues above before proceeding.")

if __name__ == "__main__":
    main()
