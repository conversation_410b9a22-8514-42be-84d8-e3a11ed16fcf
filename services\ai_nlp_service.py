import google.generativeai as genai
import json
import os
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)

class AIMarketAnalyzer:
    """AI-powered market data analyzer using Gemini API"""
    
    def __init__(self):
        self.api_key = os.getenv('GEMINI_API_KEY')
        if self.api_key:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-2.5-flash')
        else:
            logger.warning("No Gemini API key found. AI features will be disabled.")
            self.model = None
    
    def parse_natural_language_query(self, user_query: str, available_sensors: List[Dict] = None) -> Dict[str, Any]:
        """
        Use AI to parse natural language query and convert to API parameters
        
        Args:
            user_query: Natural language query from user
            available_sensors: List of available sensors with descriptions
            
        Returns:
            Dictionary with parsed parameters for API calls
        """
        if not self.model:
            return self._fallback_parsing(user_query)
        
        try:
            # Create context about available sensors
            sensor_context = ""
            if available_sensors:
                sensor_context = "\n\nAvailable sensors:\n"
                for sensor in available_sensors[:20]:  # Limit to avoid token limits
                    sensor_context += f"- ID: {sensor.get('sensor_id', 'N/A')}, Description: {sensor.get('description', 'N/A')}\n"
            
            prompt = f"""
You are an expert in electricity market data analysis. Parse this natural language query and extract the following information:

User Query: "{user_query}"

{sensor_context}

Please analyze the query and return a JSON object with these fields:

1. "iso" - The ISO/market (ERCOT, PJM, CAISO, NYISO, ISONE, MISO, SPP, etc.)
2. "data_type" - Type of data requested:
   - "lmp" for Locational Marginal Price
   - "nddam" for Next Day Day-Ahead Market
   - "rt" for Real-Time prices
   - "da" for Day-Ahead prices
   - "load" for Load/Demand data
   - "generation" for Generation data
   - "bal" for Balance/Balancing data
3. "hub_or_node" - Specific hub, node, or location if mentioned
4. "analysis_type" - What analysis to perform:
   - "rmse" for Root Mean Square Error
   - "mae" for Mean Absolute Error
   - "mape" for Mean Absolute Percentage Error
   - "volatility" for volatility analysis
   - "statistics" for basic statistics (mean, median, std)
   - "trend" for trend analysis
   - "peak_analysis" for peak vs off-peak analysis
   - "forecast" for forecasting
   - "comparison" for comparing different periods/datasets
   - "custom" for custom calculations
5. "time_period" - Time period requested:
   - "start_date" in YYYY-MM-DD format
   - "end_date" in YYYY-MM-DD format
   - "period_description" (e.g., "this week", "last month")
6. "specific_calculation" - If user wants specific calculations, describe what they want
7. "sensor_hints" - Any specific sensor IDs or partial names mentioned
8. "confidence" - Your confidence level (0-1) in the parsing

Handle abbreviations, short forms, and broken names intelligently. For example:
- "rt" = real-time
- "da" = day-ahead  
- "bal day" = balance day
- "ercot north" = ERCOT North Hub
- "pjm west" = PJM Western Hub

Current date for reference: {datetime.now().strftime('%Y-%m-%d')}

Return only valid JSON, no other text.
"""

            response = self.model.generate_content(prompt)
            
            try:
                # Clean the response text - remove markdown code blocks if present
                response_text = response.text.strip()
                if response_text.startswith('```json'):
                    response_text = response_text.split('```json')[1].split('```')[0].strip()
                elif response_text.startswith('```'):
                    response_text = response_text.split('```')[1].split('```')[0].strip()

                parsed_result = json.loads(response_text)
                logger.info(f"AI parsed query successfully: {parsed_result}")
                return parsed_result
            except json.JSONDecodeError:
                logger.error(f"Failed to parse AI response as JSON: {response.text}")
                return self._fallback_parsing(user_query)
                
        except Exception as e:
            logger.error(f"Error in AI query parsing: {str(e)}")
            return self._fallback_parsing(user_query)
    
    def find_best_sensors(self, query_params: Dict[str, Any], available_sensors: List[Dict]) -> List[Dict]:
        """
        Use AI to find the best matching sensors for the query
        
        Args:
            query_params: Parsed query parameters
            available_sensors: List of available sensors
            
        Returns:
            List of best matching sensors, ranked by relevance
        """
        if not self.model or not available_sensors:
            return []
        
        try:
            # Prepare sensor information for AI
            sensor_info = []
            for sensor in available_sensors:
                sensor_info.append({
                    'id': sensor.get('sensor_id', ''),
                    'description': sensor.get('description', ''),
                    'region': sensor.get('report_region', ''),
                    'report_name': sensor.get('report_name', '')
                })
            
            prompt = f"""
You are an expert in electricity market data. Find the best matching sensors for this query.

Query Parameters:
- ISO: {query_params.get('iso', 'N/A')}
- Data Type: {query_params.get('data_type', 'N/A')}
- Hub/Node: {query_params.get('hub_or_node', 'N/A')}
- Analysis: {query_params.get('analysis_type', 'N/A')}
- Sensor Hints: {query_params.get('sensor_hints', 'N/A')}

Available Sensors:
{json.dumps(sensor_info[:50], indent=2)}

Please rank the sensors by relevance and return a JSON array of sensor IDs in order of best match to worst match. Consider:
1. ISO/region matching
2. Data type matching (LMP, RT, DA, Load, etc.)
3. Hub/node/location matching
4. Description keywords

Return only a JSON array of sensor IDs, like: ["sensor1", "sensor2", "sensor3"]
Maximum 10 sensors.
"""

            response = self.model.generate_content(prompt)
            
            try:
                # Clean the response text - remove markdown code blocks if present
                response_text = response.text.strip()
                if response_text.startswith('```json'):
                    response_text = response_text.split('```json')[1].split('```')[0].strip()
                elif response_text.startswith('```'):
                    response_text = response_text.split('```')[1].split('```')[0].strip()

                sensor_ids = json.loads(response_text)

                # Return full sensor objects in ranked order
                ranked_sensors = []
                for sensor_id in sensor_ids:
                    for sensor in available_sensors:
                        if sensor.get('sensor_id') == sensor_id:
                            ranked_sensors.append(sensor)
                            break

                logger.info(f"AI ranked {len(ranked_sensors)} sensors")
                return ranked_sensors

            except json.JSONDecodeError:
                logger.error(f"Failed to parse AI sensor ranking: {response.text}")
                return available_sensors[:10]  # Fallback to first 10
                
        except Exception as e:
            logger.error(f"Error in AI sensor ranking: {str(e)}")
            return available_sensors[:10]  # Fallback
    
    def generate_dynamic_analysis(self, query_params: Dict[str, Any], data: Any) -> Dict[str, Any]:
        """
        Use AI to generate dynamic analysis based on user's specific request
        
        Args:
            query_params: Parsed query parameters
            data: Market data (pandas DataFrame)
            
        Returns:
            Analysis results
        """
        if not self.model:
            return {'error': 'AI analysis not available'}
        
        try:
            # Prepare data summary for AI
            if hasattr(data, 'describe'):
                data_summary = data.describe().to_string()
                data_info = f"Data shape: {data.shape}, Columns: {list(data.columns)}"
            else:
                data_summary = str(data)[:1000]
                data_info = "Data type: " + str(type(data))
            
            prompt = f"""
You are an expert data analyst for electricity markets. Generate Python code to perform the requested analysis.

Query Parameters:
{json.dumps(query_params, indent=2)}

Data Information:
{data_info}

Data Summary:
{data_summary}

User wants: {query_params.get('specific_calculation', query_params.get('analysis_type', 'basic analysis'))}

Generate Python code that:
1. Performs the requested analysis on the data
2. Returns results as a dictionary with meaningful keys
3. Includes relevant statistical measures
4. Uses pandas and numpy operations
5. Handles edge cases and missing data

The data variable is called 'data' and is a pandas DataFrame.
Return only the Python code, no explanations.

Example format:
```python
import pandas as pd
import numpy as np

# Your analysis code here
result = {{
    'metric_name': calculated_value,
    'additional_info': other_value
}}
```
"""

            response = self.model.generate_content(prompt)
            
            # Extract Python code from response
            code = response.text
            if '```python' in code:
                code = code.split('```python')[1].split('```')[0]
            elif '```' in code:
                code = code.split('```')[1]
            
            # Execute the generated code safely
            import pandas as pd
            import numpy as np
            from sklearn.metrics import mean_squared_error, mean_absolute_error
            
            local_vars = {
                'data': data,
                'pd': pd,
                'np': np,
                'mean_squared_error': mean_squared_error,
                'mean_absolute_error': mean_absolute_error
            }
            
            exec(code, {}, local_vars)
            
            if 'result' in local_vars:
                logger.info("AI generated dynamic analysis successfully")
                return local_vars['result']
            else:
                logger.error("AI code did not produce 'result' variable")
                return {'error': 'Analysis code did not produce results'}
                
        except Exception as e:
            logger.error(f"Error in AI dynamic analysis: {str(e)}")
            return {'error': f'Dynamic analysis failed: {str(e)}'}
    
    def _fallback_parsing(self, user_query: str) -> Dict[str, Any]:
        """Fallback parsing when AI is not available"""
        # Simple rule-based parsing as fallback
        query_lower = user_query.lower()
        
        # Extract ISO
        iso = None
        iso_patterns = {
            'ercot': ['ercot', 'texas'],
            'pjm': ['pjm'],
            'caiso': ['caiso', 'california'],
            'nyiso': ['nyiso', 'new york'],
            'isone': ['iso-ne', 'isone', 'new england'],
            'miso': ['miso'],
            'spp': ['spp']
        }
        
        for iso_name, patterns in iso_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                iso = iso_name
                break
        
        # Extract data type
        data_type = 'lmp'  # default
        if any(term in query_lower for term in ['nddam', 'next day']):
            data_type = 'nddam'
        elif any(term in query_lower for term in ['real time', 'rt']):
            data_type = 'rt'
        elif any(term in query_lower for term in ['day ahead', 'da']):
            data_type = 'da'
        elif any(term in query_lower for term in ['load', 'demand']):
            data_type = 'load'
        
        # Extract analysis type
        analysis_type = 'statistics'  # default
        if 'rmse' in query_lower:
            analysis_type = 'rmse'
        elif 'volatility' in query_lower:
            analysis_type = 'volatility'
        elif 'peak' in query_lower:
            analysis_type = 'peak_analysis'
        
        return {
            'iso': iso,
            'data_type': data_type,
            'analysis_type': analysis_type,
            'confidence': 0.5,
            'start_date': (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d'),
            'end_date': datetime.now().strftime('%Y-%m-%d')
        }
