from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import os
from dotenv import load_dotenv
from services.api_service import MarketDataAPI
from services.nlp_service import NLPProcessor
from services.analysis_service import DataAnalyzer
from services.response_generator import ResponseGenerator

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

# Initialize services
api_service = MarketDataAPI()
nlp_processor = NLPProcessor()
data_analyzer = DataAnalyzer()
response_generator = ResponseGenerator()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        user_message = request.json.get('message', '')
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Use AI-powered query processing
        ai_result = api_service.process_ai_query(user_message)

        if 'error' in ai_result:
            return jsonify({
                'response': ai_result['error'],
                'suggestions': ai_result.get('suggestions', []),
                'query_params': ai_result.get('query_params')
            })

        # Generate natural language response from AI analysis
        if ai_result.get('analysis_result') and 'error' not in ai_result['analysis_result']:
            # Use AI analysis result to generate response
            response_text = response_generator.generate_ai_response(
                ai_result['query_params'],
                ai_result['analysis_result'],
                ai_result['sensor_used'],
                user_message
            )
        else:
            # Fallback to traditional analysis if AI analysis failed
            query_params = ai_result['query_params']

            # Fetch market data using traditional method
            market_data = api_service.fetch_data(
                iso=query_params.get('iso'),
                hub=query_params.get('hub_or_node'),
                data_type=query_params.get('data_type'),
                start_date=query_params.get('start_date'),
                end_date=query_params.get('end_date')
            )

            if market_data is None or (hasattr(market_data, 'empty') and market_data.empty):
                return jsonify({
                    'response': f'No data available for {query_params.get("iso")} {query_params.get("data_type")} in the specified time period.'
                })

            # Perform traditional analysis
            analysis_result = data_analyzer.analyze(
                data=market_data,
                analysis_type=query_params.get('analysis_type', 'statistics'),
                parameters=query_params
            )

            if 'error' in analysis_result:
                return jsonify({
                    'response': f'Analysis failed: {analysis_result["error"]}'
                })

            # Generate traditional response
            response_data = response_generator.generate_response(
                query_params=query_params,
                analysis_result=analysis_result,
                original_query=user_message
            )
            response_text = response_data['text']

        return jsonify({
            'response': response_text,
            'data_summary': ai_result.get('data_summary'),
            'sensor_used': ai_result.get('sensor_used', {}).get('description', ''),
            'analysis_type': ai_result['query_params'].get('analysis_type'),
            'iso': ai_result['query_params'].get('iso'),
            'confidence': ai_result['query_params'].get('confidence', 0)
        })
        
    except Exception as e:
        app.logger.error(f"Error processing chat request: {str(e)}")
        return jsonify({
            'response': "I encountered an error while processing your request. Please try again or rephrase your question."
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'message': 'Market Price Chatbot is running'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
