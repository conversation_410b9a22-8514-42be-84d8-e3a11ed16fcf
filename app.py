from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import os
from datetime import datetime
from dotenv import load_dotenv
from services.api_service import MarketDataAPI
from services.nlp_service import NLPProcessor
from services.analysis_service import DataAnalyzer
from services.response_generator import ResponseGenerator
from services.roleplay_service import RoleplayService
from services.persona_manager import Persona<PERSON>anager
from services.mood_engine import MoodEngine
from services.question_generator import QuestionGenerator, QuestionContext
from services.feedback_generator import FeedbackGenerator
from services.ai_nlp_service import AIMarketAnalyzer

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

# Initialize services
api_service = MarketDataAPI()
nlp_processor = NLPProcessor()
data_analyzer = DataAnalyzer()
response_generator = ResponseGenerator()

# Initialize roleplay services
ai_analyzer = AIMarketAnalyzer()
roleplay_service = RoleplayService(ai_analyzer)
persona_manager = PersonaManager(ai_analyzer)
mood_engine = MoodEngine(ai_analyzer)
question_generator = QuestionGenerator(ai_analyzer)
feedback_generator = FeedbackGenerator(ai_analyzer)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        user_message = request.json.get('message', '')
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Use AI-powered query processing
        ai_result = api_service.process_ai_query(user_message)

        if 'error' in ai_result:
            return jsonify({
                'response': ai_result['error'],
                'suggestions': ai_result.get('suggestions', []),
                'query_params': ai_result.get('query_params')
            })

        # Generate natural language response from AI analysis
        if ai_result.get('analysis_result') and 'error' not in ai_result['analysis_result']:
            # Use AI analysis result to generate response
            response_text = response_generator.generate_ai_response(
                ai_result['query_params'],
                ai_result['analysis_result'],
                ai_result['sensor_used'],
                user_message
            )
        else:
            # Fallback to traditional analysis if AI analysis failed
            query_params = ai_result['query_params']

            # Fetch market data using traditional method
            market_data = api_service.fetch_data(
                iso=query_params.get('iso'),
                hub=query_params.get('hub_or_node'),
                data_type=query_params.get('data_type'),
                start_date=query_params.get('start_date'),
                end_date=query_params.get('end_date')
            )

            if market_data is None or (hasattr(market_data, 'empty') and market_data.empty):
                return jsonify({
                    'response': f'No data available for {query_params.get("iso")} {query_params.get("data_type")} in the specified time period.'
                })

            # Perform traditional analysis
            analysis_result = data_analyzer.analyze(
                data=market_data,
                analysis_type=query_params.get('analysis_type', 'statistics'),
                parameters=query_params
            )

            if 'error' in analysis_result:
                return jsonify({
                    'response': f'Analysis failed: {analysis_result["error"]}'
                })

            # Generate traditional response
            response_data = response_generator.generate_response(
                query_params=query_params,
                analysis_result=analysis_result,
                original_query=user_message
            )
            response_text = response_data['text']

        return jsonify({
            'response': response_text,
            'data_summary': ai_result.get('data_summary'),
            'sensor_used': ai_result.get('sensor_used', {}).get('description', ''),
            'analysis_type': ai_result['query_params'].get('analysis_type'),
            'iso': ai_result['query_params'].get('iso'),
            'confidence': ai_result['query_params'].get('confidence', 0)
        })
        
    except Exception as e:
        app.logger.error(f"Error processing chat request: {str(e)}")
        return jsonify({
            'response': "I encountered an error while processing your request. Please try again or rephrase your question."
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'message': 'Market Price Chatbot is running'})

# Roleplay Training Routes

@app.route('/api/roleplay/create', methods=['POST'])
def create_roleplay_session():
    """Create a new roleplay training session"""
    try:
        data = request.json
        persona_type = data.get('persona_type')
        mood_type = data.get('mood_type')
        agenda = data.get('agenda')
        industry = data.get('industry', 'energy')
        max_questions = data.get('max_questions', 5)

        if not all([persona_type, mood_type, agenda]):
            return jsonify({'error': 'Missing required parameters: persona_type, mood_type, agenda'}), 400

        # Create roleplay session
        session_result = roleplay_service.create_session(persona_type, mood_type, agenda, max_questions)

        if 'error' in session_result:
            return jsonify(session_result), 400

        # Generate dynamic persona profile
        dynamic_persona = persona_manager.generate_dynamic_persona(
            roleplay_service.persona_profiles[roleplay_service.active_sessions[session_result['session_id']].persona_type].persona_type,
            agenda,
            industry
        )

        # Generate first question
        session = roleplay_service.active_sessions[session_result['session_id']]
        question_context = QuestionContext(
            persona_type=session.persona_type,
            mood_type=session.mood_type,
            agenda=agenda,
            industry=industry,
            conversation_history=[],
            current_question_number=1,
            total_questions=max_questions,
            user_performance_score=5.0
        )

        first_question = question_generator.generate_question(question_context)

        return jsonify({
            'session_id': session_result['session_id'],
            'persona_profile': session_result['persona_profile'],
            'mood_profile': session_result['mood_profile'],
            'dynamic_persona': dynamic_persona,
            'context': session_result['context'],
            'first_question': {
                'question_text': first_question.question_text,
                'question_type': first_question.question_type,
                'difficulty_level': first_question.difficulty_level,
                'persona_context': first_question.persona_context
            },
            'coaching_tips': mood_engine.get_mood_coaching_tips(session.mood_type),
            'status': 'created'
        })

    except Exception as e:
        app.logger.error(f"Error creating roleplay session: {str(e)}")
        return jsonify({'error': 'Failed to create roleplay session'}), 500

@app.route('/api/roleplay/<session_id>/respond', methods=['POST'])
def respond_to_roleplay(session_id):
    """Handle user response in roleplay session"""
    try:
        data = request.json
        user_response = data.get('response', '')

        if not user_response:
            return jsonify({'error': 'No response provided'}), 400

        # Get session
        session_data = roleplay_service.get_session(session_id)
        if not session_data:
            return jsonify({'error': 'Session not found'}), 404

        session = roleplay_service.active_sessions[session_id]

        # Generate feedback for user response
        question_context = {
            'question_text': data.get('question_text', ''),
            'question_type': data.get('question_type', 'general'),
            'expected_topics': data.get('expected_topics', [])
        }

        feedback = feedback_generator.generate_feedback(
            user_response, question_context, session.persona_type, session.mood_type
        )

        # Update session with conversation history and feedback
        session.conversation_history.append({
            'question': question_context['question_text'],
            'user_response': user_response,
            'timestamp': str(datetime.now()),
            'topic': session.agenda
        })

        session.feedback_history.append({
            'score': feedback.overall_score,
            'feedback': feedback,
            'timestamp': str(datetime.now())
        })

        # Update session metrics
        session.current_question_count += 1
        session.strengths.extend(feedback.strengths)
        session.improvement_areas.extend(feedback.improvement_areas)
        session.missed_points.extend(feedback.missed_opportunities)

        # Calculate average performance
        scores = [f['score'] for f in session.feedback_history]
        avg_score = sum(scores) / len(scores) if scores else 5.0

        # Generate next question if session continues
        next_question = None
        session_complete = session.current_question_count >= session.max_questions

        if not session_complete:
            question_context_next = QuestionContext(
                persona_type=session.persona_type,
                mood_type=session.mood_type,
                agenda=session.agenda,
                industry=data.get('industry', 'energy'),
                conversation_history=session.conversation_history,
                current_question_number=session.current_question_count + 1,
                total_questions=session.max_questions,
                user_performance_score=avg_score
            )

            next_question_obj = question_generator.generate_question(question_context_next)
            next_question = {
                'question_text': next_question_obj.question_text,
                'question_type': next_question_obj.question_type,
                'difficulty_level': next_question_obj.difficulty_level,
                'expected_topics': next_question_obj.expected_topics,
                'persona_context': next_question_obj.persona_context
            }

        # Generate mood response
        mood_response = mood_engine.generate_mood_response(
            session.mood_type,
            {'agenda': session.agenda, 'industry': data.get('industry', 'energy')},
            feedback.overall_score / 10.0
        )

        return jsonify({
            'feedback': {
                'overall_score': feedback.overall_score,
                'strengths': feedback.strengths,
                'improvement_areas': feedback.improvement_areas,
                'specific_suggestions': feedback.specific_suggestions,
                'missed_opportunities': feedback.missed_opportunities,
                'next_steps': feedback.next_steps
            },
            'mood_response': mood_response,
            'next_question': next_question,
            'session_progress': {
                'current_question': session.current_question_count,
                'total_questions': session.max_questions,
                'average_score': avg_score,
                'session_complete': session_complete
            },
            'status': 'feedback_provided'
        })

    except Exception as e:
        app.logger.error(f"Error processing roleplay response: {str(e)}")
        return jsonify({'error': 'Failed to process response'}), 500

@app.route('/api/roleplay/<session_id>/complete', methods=['POST'])
def complete_roleplay_session(session_id):
    """Complete a roleplay session and get final evaluation"""
    try:
        evaluation_result = roleplay_service.end_session(session_id)

        if 'error' in evaluation_result:
            return jsonify(evaluation_result), 404

        return jsonify(evaluation_result)

    except Exception as e:
        app.logger.error(f"Error completing roleplay session: {str(e)}")
        return jsonify({'error': 'Failed to complete session'}), 500

@app.route('/api/roleplay/<session_id>/status', methods=['GET'])
def get_roleplay_status(session_id):
    """Get current status of roleplay session"""
    try:
        session_data = roleplay_service.get_session(session_id)

        if not session_data:
            return jsonify({'error': 'Session not found'}), 404

        # Return simplified session data to avoid serialization issues
        session = roleplay_service.active_sessions[session_id]
        response_data = {
            'session': {
                'session_id': session_id,
                'persona_type': session.persona_type.value,
                'mood_type': session.mood_type.value,
                'agenda': session.agenda,
                'status': session.status.value,
                'current_question_count': session.current_question_count,
                'max_questions': session.max_questions,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat()
            },
            'persona_profile': roleplay_service._serialize_persona_profile(
                roleplay_service.persona_profiles[session.persona_type]
            ),
            'mood_profile': roleplay_service._serialize_mood_profile(
                roleplay_service.mood_profiles[session.mood_type]
            )
        }

        return jsonify(response_data)

    except Exception as e:
        app.logger.error(f"Error getting roleplay status: {str(e)}")
        return jsonify({'error': 'Failed to get session status'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
