#!/usr/bin/env python3
"""
Test the fixed AI chatbot
"""

import requests
import json
import time

def test_fixed_chatbot():
    """Test the fixed AI chatbot"""
    
    print("🤖 TESTING FIXED AI CHATBOT")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test queries
    test_queries = [
        "ERCOT LMP statistics this week",
        "What is the RMSE for PJM north hub?", 
        "Show me volatility for CAISO",
        "Peak analysis for NYISO",
        "MISO balance day prices"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: '{query}'")
        print("-" * 40)
        
        try:
            response = requests.post(
                f"{base_url}/api/chat",
                json={"message": query},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if 'response' in result and len(result['response']) > 50:
                    print("✅ SUCCESS! AI Response:")
                    
                    # Show first few lines of response
                    lines = result['response'].split('\n')
                    for line in lines[:6]:
                        if line.strip():
                            print(f"   {line}")
                    
                    # Show additional info
                    if result.get('data_summary'):
                        data_summary = result['data_summary']
                        print(f"\n📊 Data: {data_summary.get('data_points', 'N/A')} points")
                        
                    if result.get('confidence'):
                        print(f"🎯 Confidence: {result['confidence']:.1%}")
                        
                else:
                    print(f"⚠️ Short response: {result.get('response', 'No response')}")
                    
            else:
                print(f"❌ HTTP {response.status_code}: {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        time.sleep(1)
    
    print(f"\n🎉 Testing complete! Check the web interface at {base_url}")

if __name__ == "__main__":
    test_fixed_chatbot()
