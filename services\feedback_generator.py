"""
Feedback Generator - Real-time feedback and evaluation for roleplay training
"""

import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from services.roleplay_service import PersonaType, MoodType

logger = logging.getLogger(__name__)

@dataclass
class FeedbackCriteria:
    """Criteria for evaluating user responses"""
    criterion_name: str
    weight: float
    description: str
    evaluation_points: List[str]

@dataclass
class FeedbackResult:
    """Result of feedback evaluation"""
    overall_score: float
    criterion_scores: Dict[str, float]
    strengths: List[str]
    improvement_areas: List[str]
    specific_suggestions: List[str]
    missed_opportunities: List[str]
    next_steps: List[str]

class FeedbackGenerator:
    """AI-powered feedback generator for roleplay training"""
    
    def __init__(self, ai_analyzer=None):
        self.ai_analyzer = ai_analyzer
        self.evaluation_criteria = self._initialize_evaluation_criteria()
        self.feedback_templates = self._initialize_feedback_templates()
        self.scoring_rubrics = self._initialize_scoring_rubrics()
        
    def _initialize_evaluation_criteria(self) -> Dict[str, List[FeedbackCriteria]]:
        """Initialize evaluation criteria by question type"""
        return {
            'technical_knowledge': [
                FeedbackCriteria(
                    criterion_name='Technical Accuracy',
                    weight=0.3,
                    description='Accuracy and depth of technical information provided',
                    evaluation_points=[
                        'Provides correct technical details',
                        'Demonstrates understanding of technical concepts',
                        'Uses appropriate technical terminology',
                        'Addresses technical concerns accurately'
                    ]
                ),
                FeedbackCriteria(
                    criterion_name='Clarity of Explanation',
                    weight=0.25,
                    description='Ability to explain technical concepts clearly',
                    evaluation_points=[
                        'Explains complex topics in understandable terms',
                        'Uses appropriate analogies and examples',
                        'Structures explanation logically',
                        'Adapts explanation to audience level'
                    ]
                ),
                FeedbackCriteria(
                    criterion_name='Practical Application',
                    weight=0.25,
                    description='Connects technical concepts to practical business value',
                    evaluation_points=[
                        'Shows how technology solves business problems',
                        'Provides real-world implementation examples',
                        'Addresses practical considerations',
                        'Discusses implementation challenges'
                    ]
                ),
                FeedbackCriteria(
                    criterion_name='Confidence and Credibility',
                    weight=0.2,
                    description='Demonstrates confidence and establishes credibility',
                    evaluation_points=[
                        'Speaks with confidence about technical topics',
                        'Acknowledges limitations appropriately',
                        'Provides credible sources or references',
                        'Maintains professional demeanor'
                    ]
                )
            ],
            'business_acumen': [
                FeedbackCriteria(
                    criterion_name='Business Value Articulation',
                    weight=0.35,
                    description='Ability to articulate clear business value',
                    evaluation_points=[
                        'Clearly states business benefits',
                        'Quantifies value where possible',
                        'Connects to business objectives',
                        'Addresses ROI and cost considerations'
                    ]
                ),
                FeedbackCriteria(
                    criterion_name='Market Understanding',
                    weight=0.25,
                    description='Demonstrates understanding of market dynamics',
                    evaluation_points=[
                        'Shows awareness of industry trends',
                        'Understands competitive landscape',
                        'Recognizes market challenges',
                        'Identifies market opportunities'
                    ]
                ),
                FeedbackCriteria(
                    criterion_name='Risk Assessment',
                    weight=0.2,
                    description='Identifies and addresses business risks',
                    evaluation_points=[
                        'Identifies potential risks',
                        'Proposes risk mitigation strategies',
                        'Discusses contingency plans',
                        'Balances risk and reward'
                    ]
                ),
                FeedbackCriteria(
                    criterion_name='Strategic Thinking',
                    weight=0.2,
                    description='Demonstrates strategic perspective',
                    evaluation_points=[
                        'Considers long-term implications',
                        'Aligns with strategic objectives',
                        'Thinks beyond immediate needs',
                        'Considers broader organizational impact'
                    ]
                )
            ],
            'communication_skills': [
                FeedbackCriteria(
                    criterion_name='Clarity and Conciseness',
                    weight=0.3,
                    description='Communicates clearly and concisely',
                    evaluation_points=[
                        'Uses clear, understandable language',
                        'Avoids unnecessary jargon',
                        'Structures responses logically',
                        'Stays focused on key points'
                    ]
                ),
                FeedbackCriteria(
                    criterion_name='Audience Adaptation',
                    weight=0.25,
                    description='Adapts communication style to audience',
                    evaluation_points=[
                        'Matches communication style to persona',
                        'Adjusts technical depth appropriately',
                        'Responds to mood and engagement level',
                        'Uses appropriate examples for audience'
                    ]
                ),
                FeedbackCriteria(
                    criterion_name='Active Listening',
                    weight=0.25,
                    description='Demonstrates active listening skills',
                    evaluation_points=[
                        'Addresses specific questions asked',
                        'Builds on previous conversation points',
                        'Acknowledges concerns raised',
                        'Asks clarifying questions when needed'
                    ]
                ),
                FeedbackCriteria(
                    criterion_name='Professional Presence',
                    weight=0.2,
                    description='Maintains professional demeanor and presence',
                    evaluation_points=[
                        'Maintains confident and professional tone',
                        'Handles challenging questions gracefully',
                        'Shows empathy and understanding',
                        'Builds rapport effectively'
                    ]
                )
            ]
        }
    
    def _initialize_feedback_templates(self) -> Dict[str, Dict[str, List[str]]]:
        """Initialize feedback templates for different scenarios"""
        return {
            'strengths': {
                'technical_accuracy': [
                    "Excellent technical knowledge demonstrated",
                    "Provided accurate and detailed technical information",
                    "Showed deep understanding of technical concepts",
                    "Used appropriate technical terminology effectively"
                ],
                'business_value': [
                    "Clearly articulated business value proposition",
                    "Effectively connected solution to business outcomes",
                    "Provided compelling ROI justification",
                    "Demonstrated strong business acumen"
                ],
                'communication': [
                    "Communicated complex ideas clearly",
                    "Adapted explanation well to audience level",
                    "Maintained professional and confident demeanor",
                    "Showed excellent active listening skills"
                ]
            },
            'improvements': {
                'technical_depth': [
                    "Could provide more technical detail and specifics",
                    "Consider including more implementation examples",
                    "Address potential technical challenges more thoroughly",
                    "Explain technical concepts in simpler terms"
                ],
                'business_focus': [
                    "Strengthen the business value proposition",
                    "Provide more specific ROI calculations",
                    "Better connect features to business benefits",
                    "Address cost considerations more directly"
                ],
                'communication_style': [
                    "Adapt communication style better to audience mood",
                    "Be more concise in responses",
                    "Use more relevant examples for this industry",
                    "Show more empathy for customer concerns"
                ]
            },
            'suggestions': {
                'persona_specific': {
                    'client': [
                        "Focus more on business outcomes and ROI",
                        "Address risk mitigation strategies",
                        "Provide implementation timeline details",
                        "Include change management considerations"
                    ],
                    'vendor': [
                        "Emphasize partnership value and support",
                        "Highlight technical capabilities and differentiators",
                        "Address integration and scalability concerns",
                        "Provide customer success examples"
                    ],
                    'consultant': [
                        "Include industry best practices and benchmarks",
                        "Address strategic implications and alternatives",
                        "Discuss change management and adoption strategies",
                        "Provide framework for decision-making"
                    ]
                }
            }
        }
    
    def _initialize_scoring_rubrics(self) -> Dict[str, Dict[str, str]]:
        """Initialize scoring rubrics for consistent evaluation"""
        return {
            'excellent': {
                'range': '8.5-10.0',
                'description': 'Exceptional response that fully addresses the question with depth, accuracy, and professionalism'
            },
            'good': {
                'range': '7.0-8.4',
                'description': 'Strong response that addresses most aspects well with minor areas for improvement'
            },
            'satisfactory': {
                'range': '5.5-6.9',
                'description': 'Adequate response that covers basic requirements but lacks depth or has some gaps'
            },
            'needs_improvement': {
                'range': '4.0-5.4',
                'description': 'Response addresses some aspects but has significant gaps or inaccuracies'
            },
            'poor': {
                'range': '0.0-3.9',
                'description': 'Response fails to adequately address the question or contains major errors'
            }
        }
    
    def generate_feedback(self, user_response: str, question_context: Dict[str, Any], 
                         persona_type: PersonaType, mood_type: MoodType) -> FeedbackResult:
        """Generate comprehensive feedback for user response"""
        try:
            if self.ai_analyzer and self.ai_analyzer.model:
                return self._generate_ai_feedback(user_response, question_context, persona_type, mood_type)
            else:
                return self._generate_template_feedback(user_response, question_context, persona_type, mood_type)
                
        except Exception as e:
            logger.error(f"Error generating feedback: {e}")
            return self._generate_fallback_feedback(user_response)
    
    def _generate_ai_feedback(self, user_response: str, question_context: Dict[str, Any], 
                            persona_type: PersonaType, mood_type: MoodType) -> FeedbackResult:
        """Generate feedback using AI analysis"""
        try:
            question_type = question_context.get('question_type', 'general')
            criteria = self.evaluation_criteria.get(question_type, self.evaluation_criteria['communication_skills'])
            
            # Prepare criteria for AI
            criteria_text = ""
            for criterion in criteria:
                criteria_text += f"\n- {criterion.criterion_name} (Weight: {criterion.weight}): {criterion.description}"
                for point in criterion.evaluation_points:
                    criteria_text += f"\n  • {point}"
            
            prompt = f"""
You are an expert business communication coach evaluating a roleplay training response.

Context:
- Question: {question_context.get('question_text', 'N/A')}
- Question Type: {question_type}
- Persona: {persona_type.value.title()} ({self._get_persona_expectations(persona_type)})
- Mood: {mood_type.value.title()} ({self._get_mood_expectations(mood_type)})
- Expected Topics: {question_context.get('expected_topics', [])}

User Response: "{user_response}"

Evaluation Criteria:{criteria_text}

Evaluate the response and provide detailed feedback. Consider:
1. How well the response addresses the specific question
2. Appropriateness for the persona type and mood
3. Technical accuracy and business relevance
4. Communication effectiveness
5. Missed opportunities or gaps

Return a JSON object with:
{{
    "overall_score": 7.5,
    "criterion_scores": {{
        "criterion_name": score_out_of_10
    }},
    "strengths": ["strength1", "strength2", "strength3"],
    "improvement_areas": ["area1", "area2"],
    "specific_suggestions": ["suggestion1", "suggestion2", "suggestion3"],
    "missed_opportunities": ["opportunity1", "opportunity2"],
    "next_steps": ["step1", "step2"]
}}

Provide constructive, specific, and actionable feedback. Return only valid JSON.
"""

            response = self.ai_analyzer.model.generate_content(prompt)
            
            # Parse AI response
            response_text = response.text.strip()
            if response_text.startswith('```json'):
                response_text = response_text.split('```json')[1].split('```')[0].strip()
            elif response_text.startswith('```'):
                response_text = response_text.split('```')[1].split('```')[0].strip()
            
            feedback_data = json.loads(response_text)
            
            return FeedbackResult(
                overall_score=feedback_data['overall_score'],
                criterion_scores=feedback_data['criterion_scores'],
                strengths=feedback_data['strengths'],
                improvement_areas=feedback_data['improvement_areas'],
                specific_suggestions=feedback_data['specific_suggestions'],
                missed_opportunities=feedback_data['missed_opportunities'],
                next_steps=feedback_data['next_steps']
            )
            
        except Exception as e:
            logger.error(f"AI feedback generation failed: {e}")
            return self._generate_template_feedback(user_response, question_context, persona_type, mood_type)
    
    def _generate_template_feedback(self, user_response: str, question_context: Dict[str, Any], 
                                  persona_type: PersonaType, mood_type: MoodType) -> FeedbackResult:
        """Generate feedback using templates as fallback"""
        try:
            question_type = question_context.get('question_type', 'communication_skills')
            criteria = self.evaluation_criteria.get(question_type, self.evaluation_criteria['communication_skills'])
            
            # Simple scoring based on response length and content
            response_length = len(user_response.split())
            has_examples = any(word in user_response.lower() for word in ['example', 'instance', 'case', 'such as'])
            addresses_business = any(word in user_response.lower() for word in ['business', 'value', 'roi', 'cost', 'benefit'])
            
            # Calculate scores
            criterion_scores = {}
            total_score = 0
            
            for criterion in criteria:
                # Simple heuristic scoring
                score = 5.0  # Base score
                
                if criterion.criterion_name == 'Technical Accuracy':
                    if response_length > 50:
                        score += 1.5
                    if 'technical' in user_response.lower():
                        score += 1.0
                elif criterion.criterion_name == 'Business Value Articulation':
                    if addresses_business:
                        score += 2.0
                    if any(word in user_response.lower() for word in ['roi', 'return', 'investment']):
                        score += 1.0
                elif criterion.criterion_name == 'Clarity and Conciseness':
                    if 20 <= response_length <= 100:
                        score += 2.0
                    elif response_length > 100:
                        score += 1.0
                
                if has_examples:
                    score += 0.5
                
                score = min(10.0, max(1.0, score))  # Clamp between 1-10
                criterion_scores[criterion.criterion_name] = score
                total_score += score * criterion.weight
            
            overall_score = min(10.0, max(1.0, total_score))
            
            # Generate feedback based on score
            strengths = self._select_strengths(overall_score, user_response)
            improvement_areas = self._select_improvements(overall_score, question_type)
            suggestions = self._select_suggestions(persona_type, mood_type)
            
            return FeedbackResult(
                overall_score=overall_score,
                criterion_scores=criterion_scores,
                strengths=strengths,
                improvement_areas=improvement_areas,
                specific_suggestions=suggestions,
                missed_opportunities=self._identify_missed_opportunities(user_response, question_context),
                next_steps=self._generate_next_steps(overall_score, persona_type)
            )
            
        except Exception as e:
            logger.error(f"Template feedback generation failed: {e}")
            return self._generate_fallback_feedback(user_response)
    
    def _select_strengths(self, score: float, response: str) -> List[str]:
        """Select appropriate strengths based on score and content"""
        strengths = []
        
        if score >= 7.0:
            strengths.append("Provided a comprehensive and well-structured response")
        if len(response.split()) > 30:
            strengths.append("Gave detailed explanation with good depth")
        if any(word in response.lower() for word in ['example', 'instance', 'case']):
            strengths.append("Included relevant examples to support points")
        if any(word in response.lower() for word in ['business', 'value', 'benefit']):
            strengths.append("Connected solution to business value")
        
        return strengths[:3]  # Limit to 3 strengths
    
    def _select_improvements(self, score: float, question_type: str) -> List[str]:
        """Select improvement areas based on score and question type"""
        improvements = []
        
        if score < 6.0:
            improvements.append("Provide more detailed and specific information")
        if score < 7.0:
            improvements.append("Better address the specific question asked")
        
        type_improvements = self.feedback_templates['improvements']
        if question_type == 'technical_knowledge':
            improvements.extend(type_improvements['technical_depth'][:1])
        elif question_type == 'business_acumen':
            improvements.extend(type_improvements['business_focus'][:1])
        else:
            improvements.extend(type_improvements['communication_style'][:1])
        
        return improvements[:2]  # Limit to 2 improvements
    
    def _select_suggestions(self, persona_type: PersonaType, mood_type: MoodType) -> List[str]:
        """Select persona-specific suggestions"""
        persona_suggestions = self.feedback_templates['suggestions']['persona_specific']
        suggestions = persona_suggestions.get(persona_type.value, [])
        
        # Add mood-specific suggestions
        if mood_type == MoodType.SKEPTICAL:
            suggestions.append("Provide more evidence and proof points to address skepticism")
        elif mood_type == MoodType.NOT_INTERESTED:
            suggestions.append("Lead with the most compelling value proposition")
        elif mood_type == MoodType.CURIOUS:
            suggestions.append("Offer additional details and resources for further exploration")
        
        return suggestions[:3]  # Limit to 3 suggestions
    
    def _identify_missed_opportunities(self, response: str, question_context: Dict[str, Any]) -> List[str]:
        """Identify missed opportunities in the response"""
        missed = []
        expected_topics = question_context.get('expected_topics', [])
        
        for topic in expected_topics:
            if topic.lower() not in response.lower():
                missed.append(f"Could have addressed {topic} in more detail")
        
        if 'roi' not in response.lower() and 'return' not in response.lower():
            missed.append("Missed opportunity to discuss ROI or financial benefits")
        
        return missed[:2]  # Limit to 2 missed opportunities
    
    def _generate_next_steps(self, score: float, persona_type: PersonaType) -> List[str]:
        """Generate next steps based on performance"""
        steps = []
        
        if score < 6.0:
            steps.append("Practice explaining key concepts more clearly")
            steps.append("Prepare specific examples for common questions")
        elif score < 8.0:
            steps.append("Work on connecting features to business benefits")
            steps.append("Practice adapting communication style to different personas")
        else:
            steps.append("Continue building on strong foundation")
            steps.append("Focus on advanced questioning and objection handling")
        
        return steps
    
    def _get_persona_expectations(self, persona_type: PersonaType) -> str:
        """Get expectations for persona type"""
        expectations = {
            PersonaType.CLIENT: "Expects business value focus, risk mitigation, and clear ROI",
            PersonaType.VENDOR: "Expects technical capabilities, partnership value, and customer success",
            PersonaType.CONSULTANT: "Expects strategic thinking, best practices, and implementation guidance"
        }
        return expectations.get(persona_type, "Professional business communication")
    
    def _get_mood_expectations(self, mood_type: MoodType) -> str:
        """Get expectations for mood type"""
        expectations = {
            MoodType.CURIOUS: "Expects detailed explanations and additional resources",
            MoodType.SKEPTICAL: "Expects evidence, proof points, and risk mitigation",
            MoodType.INDIFFERENT: "Expects concise, fact-based information",
            MoodType.PASSIVE: "Expects clear, structured information",
            MoodType.NOT_INTERESTED: "Expects compelling value propositions and quick wins"
        }
        return expectations.get(mood_type, "Professional interaction")
    
    def _generate_fallback_feedback(self, response: str) -> FeedbackResult:
        """Generate basic fallback feedback"""
        return FeedbackResult(
            overall_score=6.0,
            criterion_scores={'Overall': 6.0},
            strengths=["Provided a response to the question"],
            improvement_areas=["Could provide more detail and specificity"],
            specific_suggestions=["Practice explaining concepts more clearly", "Include relevant examples"],
            missed_opportunities=["Could have addressed business value more directly"],
            next_steps=["Continue practicing communication skills", "Prepare specific examples for common scenarios"]
        )
