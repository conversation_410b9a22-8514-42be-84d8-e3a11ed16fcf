import unittest
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.nlp_service import NLPProcessor

class TestNLPService(unittest.TestCase):
    """Test cases for the NLP service"""
    
    def setUp(self):
        self.nlp = NLPProcessor()
    
    def test_extract_iso_ercot(self):
        """Test ERCOT ISO extraction"""
        query = "What is the RMSE for ERCOT North Hub this week?"
        result = self.nlp.parse_query(query)
        self.assertIsNotNone(result)
        self.assertEqual(result['iso'], 'ercot')
    
    def test_extract_iso_pjm(self):
        """Test PJM ISO extraction"""
        query = "Show me PJM Western Hub volatility"
        result = self.nlp.parse_query(query)
        self.assertIsNotNone(result)
        self.assertEqual(result['iso'], 'pjm')
    
    def test_extract_hub_north(self):
        """Test hub extraction"""
        query = "What is the RMSE for ERCOT North Hub this week?"
        result = self.nlp.parse_query(query)
        self.assertIsNotNone(result)
        self.assertEqual(result['hub'], 'north')
    
    def test_extract_analysis_type_rmse(self):
        """Test RMSE analysis type extraction"""
        query = "What is the RMSE for ERCOT North Hub this week?"
        result = self.nlp.parse_query(query)
        self.assertIsNotNone(result)
        self.assertEqual(result['analysis_type'], 'rmse')
    
    def test_extract_analysis_type_volatility(self):
        """Test volatility analysis type extraction"""
        query = "Show me volatility analysis for PJM"
        result = self.nlp.parse_query(query)
        self.assertIsNotNone(result)
        self.assertEqual(result['analysis_type'], 'volatility')
    
    def test_extract_time_period_this_week(self):
        """Test time period extraction"""
        query = "What is the RMSE for ERCOT this week?"
        result = self.nlp.parse_query(query)
        self.assertIsNotNone(result)
        self.assertIsNotNone(result['start_date'])
        self.assertIsNotNone(result['end_date'])
    
    def test_extract_entities(self):
        """Test entity extraction"""
        query = "Compare ERCOT and PJM volatility this month"
        entities = self.nlp.extract_entities(query)
        
        self.assertIn('ercot', entities['isos'])
        self.assertIn('pjm', entities['isos'])
        self.assertIn('volatility', entities['metrics'])
        self.assertIn('this_month', entities['time_periods'])
    
    def test_query_intent_information_request(self):
        """Test query intent detection"""
        query = "What is the RMSE for ERCOT?"
        intent = self.nlp.get_query_intent(query)
        self.assertEqual(intent, 'information_request')
    
    def test_query_intent_comparison(self):
        """Test comparison intent detection"""
        query = "Compare ERCOT vs PJM prices"
        intent = self.nlp.get_query_intent(query)
        self.assertEqual(intent, 'comparison')
    
    def test_query_intent_prediction(self):
        """Test prediction intent detection"""
        query = "Forecast ERCOT prices for next 24 hours"
        intent = self.nlp.get_query_intent(query)
        self.assertEqual(intent, 'prediction')
    
    def test_no_iso_returns_none(self):
        """Test that queries without ISO return None"""
        query = "What is the weather like today?"
        result = self.nlp.parse_query(query)
        self.assertIsNone(result)
    
    def test_complex_query_parsing(self):
        """Test parsing of complex queries"""
        query = "Calculate the 24-hour moving average RMSE for CAISO SP15 hub last month"
        result = self.nlp.parse_query(query)
        
        self.assertIsNotNone(result)
        self.assertEqual(result['iso'], 'caiso')
        self.assertEqual(result['hub'], 'sp15')
        self.assertEqual(result['analysis_type'], 'rmse')
        self.assertEqual(result['parameters']['window'], 24)

if __name__ == '__main__':
    unittest.main()
