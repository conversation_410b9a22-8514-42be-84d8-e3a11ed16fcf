#!/usr/bin/env python3
"""
Test script to demonstrate the Market Price Analysis Chatbot functionality
"""

import sys
import os
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.nlp_service import NLPProcessor
from services.api_service import MarketDataAPI
from services.analysis_service import DataAnalyzer
from services.response_generator import ResponseGenerator

def test_nlp_parsing():
    """Test NLP query parsing"""
    print("=" * 60)
    print("TESTING NLP QUERY PARSING")
    print("=" * 60)
    
    nlp = NLPProcessor()
    
    test_queries = [
        "NDDAM price of ERCOT this week",
        "What is the RMSE for ERCOT LMP North Hub?",
        "Show me volatility for PJM day-ahead prices",
        "Analyze CAISO load trends last month",
        "Real-time LMP statistics for NYISO",
        "Peak hours analysis for MISO this week"
    ]
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        result = nlp.parse_query(query)
        if result:
            print(f"  ISO: {result.get('iso')}")
            print(f"  Hub: {result.get('hub')}")
            print(f"  Data Type: {result.get('data_type')}")
            print(f"  Analysis Type: {result.get('analysis_type')}")
            print(f"  Date Range: {result.get('start_date')} to {result.get('end_date')}")
        else:
            print("  Could not parse query")

def test_api_integration():
    """Test API integration with mock data"""
    print("\n" + "=" * 60)
    print("TESTING API INTEGRATION")
    print("=" * 60)
    
    api = MarketDataAPI()
    
    # Test sensor search
    print("\n1. Testing sensor search...")
    sensors = api.search_sensors(['ercot', 'lmp', 'north'], limit=5)
    print(f"Found {len(sensors)} sensors matching 'ercot lmp north'")
    for sensor in sensors[:3]:
        print(f"  - {sensor.get('sensor_id')}: {sensor.get('description')}")
    
    # Test sensor finding
    print("\n2. Testing sensor finding...")
    sensor_id = api.find_sensor_for_query('ercot', 'lmp', 'north')
    print(f"Best sensor for 'ERCOT LMP North': {sensor_id}")
    
    # Test data fetching (will use mock data)
    print("\n3. Testing data fetching...")
    data = api.fetch_data('ercot', 'north', data_type='lmp')
    if data is not None:
        print(f"Fetched {len(data)} data points")
        print(f"Date range: {data['timestamp'].min()} to {data['timestamp'].max()}")
        print(f"Price range: ${data['price'].min():.2f} - ${data['price'].max():.2f}/MWh")
    else:
        print("No data returned")

def test_analysis_engine():
    """Test analysis engine"""
    print("\n" + "=" * 60)
    print("TESTING ANALYSIS ENGINE")
    print("=" * 60)
    
    # Get some mock data
    api = MarketDataAPI()
    data = api.fetch_data('ercot', 'north', data_type='lmp')
    
    if data is None:
        print("No data available for analysis")
        return
    
    analyzer = DataAnalyzer()
    
    # Test different analysis types
    analysis_types = ['rmse', 'volatility', 'statistics', 'peak_analysis', 'price_trend']
    
    for analysis_type in analysis_types:
        print(f"\n{analysis_type.upper()} Analysis:")
        result = analyzer.analyze(data, analysis_type)
        
        if 'error' in result:
            print(f"  Error: {result['error']}")
        else:
            # Print key metrics
            for key, value in result.items():
                if key not in ['data_points', 'date_range']:
                    if isinstance(value, (int, float)):
                        print(f"  {key}: {value:.3f}")
                    else:
                        print(f"  {key}: {value}")

def test_response_generation():
    """Test response generation"""
    print("\n" + "=" * 60)
    print("TESTING RESPONSE GENERATION")
    print("=" * 60)
    
    # Simulate a complete query flow
    query = "NDDAM price of ERCOT this week"
    
    # Parse query
    nlp = NLPProcessor()
    query_params = nlp.parse_query(query)
    
    if not query_params:
        print("Could not parse query")
        return
    
    # Get data
    api = MarketDataAPI()
    data = api.fetch_data(
        iso=query_params['iso'],
        hub=query_params.get('hub'),
        data_type=query_params['data_type']
    )
    
    if data is None:
        print("No data available")
        return
    
    # Analyze data
    analyzer = DataAnalyzer()
    analysis_result = analyzer.analyze(data, query_params['analysis_type'])
    
    # Generate response
    response_gen = ResponseGenerator()
    response = response_gen.generate_response(query_params, analysis_result, query)
    
    print(f"\nOriginal Query: '{query}'")
    print("\nGenerated Response:")
    print("-" * 40)
    print(response['text'])
    
    if response.get('data_summary'):
        print("\nData Summary:")
        for key, value in response['data_summary'].items():
            print(f"  {key}: {value}")

def test_end_to_end():
    """Test complete end-to-end functionality"""
    print("\n" + "=" * 60)
    print("TESTING END-TO-END FUNCTIONALITY")
    print("=" * 60)
    
    # Initialize all services
    nlp = NLPProcessor()
    api = MarketDataAPI()
    analyzer = DataAnalyzer()
    response_gen = ResponseGenerator()
    
    test_queries = [
        "NDDAM price of ERCOT this week",
        "What is the RMSE for PJM LMP?",
        "Show volatility analysis for CAISO",
        "Peak hours for NYISO last week"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Processing: '{query}'")
        print("-" * 50)
        
        # Parse query
        query_params = nlp.parse_query(query)
        if not query_params:
            print("❌ Could not parse query")
            continue
        
        print(f"✅ Parsed: {query_params['iso']} {query_params['data_type']} {query_params['analysis_type']}")
        
        # Fetch data
        data = api.fetch_data(
            iso=query_params['iso'],
            hub=query_params.get('hub'),
            data_type=query_params['data_type']
        )
        
        if data is None:
            print("❌ No data available")
            continue
        
        print(f"✅ Data: {len(data)} points from {data['timestamp'].min().date()} to {data['timestamp'].max().date()}")
        
        # Analyze
        analysis_result = analyzer.analyze(data, query_params['analysis_type'])
        if 'error' in analysis_result:
            print(f"❌ Analysis error: {analysis_result['error']}")
            continue
        
        print("✅ Analysis completed")
        
        # Generate response
        response = response_gen.generate_response(query_params, analysis_result, query)
        print("✅ Response generated")
        
        # Show brief response
        response_lines = response['text'].split('\n')
        print(f"📊 Response preview: {response_lines[0][:100]}...")

def main():
    """Run all tests"""
    print("Market Price Analysis Chatbot - Test Suite")
    print(f"Test started at: {datetime.now()}")
    
    try:
        test_nlp_parsing()
        test_api_integration()
        test_analysis_engine()
        test_response_generation()
        test_end_to_end()
        
        print("\n" + "=" * 60)
        print("ALL TESTS COMPLETED SUCCESSFULLY! 🎉")
        print("=" * 60)
        print("\nThe chatbot is ready to use. Run 'python app.py' to start the web interface.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
