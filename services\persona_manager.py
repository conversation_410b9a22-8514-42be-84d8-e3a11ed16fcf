"""
Persona Manager - Advanced persona definitions and contextual profile generation
"""

import logging
import random
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from services.roleplay_service import PersonaType, PersonaProfile

logger = logging.getLogger(__name__)

@dataclass
class IndustryContext:
    """Industry-specific context for personas"""
    industry: str
    current_trends: List[str]
    key_challenges: List[str]
    regulatory_environment: List[str]
    market_dynamics: List[str]
    technology_drivers: List[str]

@dataclass
class CompanyProfile:
    """Detailed company profile for persona context"""
    company_size: str
    revenue_range: str
    geographic_presence: str
    business_model: str
    competitive_position: str
    recent_initiatives: List[str]
    strategic_goals: List[str]

class PersonaManager:
    """Advanced persona management with dynamic context generation"""
    
    def __init__(self, ai_analyzer=None):
        self.ai_analyzer = ai_analyzer
        self.industry_contexts = self._initialize_industry_contexts()
        self.company_profiles = self._initialize_company_profiles()
        self.persona_variations = self._initialize_persona_variations()
        
    def _initialize_industry_contexts(self) -> Dict[str, IndustryContext]:
        """Initialize industry-specific contexts"""
        return {
            'energy': IndustryContext(
                industry='Energy & Utilities',
                current_trends=[
                    'Energy transition and decarbonization',
                    'Renewable energy integration',
                    'Grid modernization and smart infrastructure',
                    'Energy storage adoption',
                    'Distributed energy resources',
                    'Electric vehicle infrastructure'
                ],
                key_challenges=[
                    'Regulatory compliance and policy changes',
                    'Price volatility and market uncertainty',
                    'Infrastructure aging and maintenance',
                    'Cybersecurity and grid resilience',
                    'Supply chain disruptions',
                    'ESG reporting and sustainability goals'
                ],
                regulatory_environment=[
                    'FERC regulations and market rules',
                    'State renewable portfolio standards',
                    'Carbon pricing mechanisms',
                    'Grid interconnection standards',
                    'Environmental compliance requirements'
                ],
                market_dynamics=[
                    'Competitive wholesale markets',
                    'Capacity market mechanisms',
                    'Ancillary services markets',
                    'Bilateral contract negotiations',
                    'Risk management and hedging strategies'
                ],
                technology_drivers=[
                    'Advanced analytics and AI',
                    'IoT and sensor technologies',
                    'Blockchain for energy trading',
                    'Digital twin technologies',
                    'Predictive maintenance systems'
                ]
            ),
            'technology': IndustryContext(
                industry='Technology',
                current_trends=[
                    'AI and machine learning adoption',
                    'Cloud-first strategies',
                    'Digital transformation acceleration',
                    'Edge computing deployment',
                    'Cybersecurity enhancement',
                    'Sustainability and green tech'
                ],
                key_challenges=[
                    'Talent acquisition and retention',
                    'Data privacy and security',
                    'Legacy system integration',
                    'Rapid technology evolution',
                    'Competitive market pressure',
                    'Regulatory compliance'
                ],
                regulatory_environment=[
                    'Data protection regulations (GDPR, CCPA)',
                    'Industry-specific compliance requirements',
                    'Cybersecurity frameworks',
                    'AI governance and ethics guidelines'
                ],
                market_dynamics=[
                    'Platform-based business models',
                    'Subscription and SaaS adoption',
                    'API economy growth',
                    'Ecosystem partnerships',
                    'Open source collaboration'
                ],
                technology_drivers=[
                    'Artificial intelligence and ML',
                    'Quantum computing research',
                    'Extended reality (AR/VR)',
                    'Autonomous systems',
                    'Biotechnology convergence'
                ]
            ),
            'financial': IndustryContext(
                industry='Financial Services',
                current_trends=[
                    'Digital banking transformation',
                    'Fintech disruption and collaboration',
                    'Open banking and APIs',
                    'Cryptocurrency and DeFi',
                    'ESG investing growth',
                    'Regulatory technology adoption'
                ],
                key_challenges=[
                    'Regulatory compliance complexity',
                    'Cybersecurity and fraud prevention',
                    'Legacy system modernization',
                    'Customer experience expectations',
                    'Market volatility management',
                    'Operational risk management'
                ],
                regulatory_environment=[
                    'Basel III capital requirements',
                    'Dodd-Frank compliance',
                    'MiFID II regulations',
                    'Anti-money laundering (AML)',
                    'Know Your Customer (KYC) requirements'
                ],
                market_dynamics=[
                    'Interest rate environment',
                    'Credit risk assessment',
                    'Liquidity management',
                    'Market making and trading',
                    'Asset management trends'
                ],
                technology_drivers=[
                    'Blockchain and distributed ledger',
                    'Artificial intelligence for risk',
                    'Robotic process automation',
                    'Cloud computing adoption',
                    'Advanced analytics platforms'
                ]
            )
        }
    
    def _initialize_company_profiles(self) -> Dict[str, List[CompanyProfile]]:
        """Initialize company profile templates by size"""
        return {
            'large_enterprise': [
                CompanyProfile(
                    company_size='Large Enterprise (10,000+ employees)',
                    revenue_range='$5B+ annual revenue',
                    geographic_presence='Global operations across multiple continents',
                    business_model='Diversified business units with multiple revenue streams',
                    competitive_position='Market leader with established brand recognition',
                    recent_initiatives=[
                        'Digital transformation program',
                        'Sustainability and ESG initiatives',
                        'M&A activity and portfolio optimization',
                        'Innovation lab and venture investments'
                    ],
                    strategic_goals=[
                        'Market share expansion',
                        'Operational efficiency improvement',
                        'New market penetration',
                        'Technology modernization',
                        'Talent development and retention'
                    ]
                )
            ],
            'mid_market': [
                CompanyProfile(
                    company_size='Mid-Market (1,000-10,000 employees)',
                    revenue_range='$100M-$5B annual revenue',
                    geographic_presence='Regional presence with selective international operations',
                    business_model='Focused business model with core competencies',
                    competitive_position='Strong regional player with growth ambitions',
                    recent_initiatives=[
                        'Technology infrastructure upgrades',
                        'Market expansion initiatives',
                        'Process optimization projects',
                        'Strategic partnership development'
                    ],
                    strategic_goals=[
                        'Revenue growth acceleration',
                        'Operational scalability',
                        'Market differentiation',
                        'Technology advancement',
                        'Competitive positioning'
                    ]
                )
            ],
            'growth_company': [
                CompanyProfile(
                    company_size='Growth Company (100-1,000 employees)',
                    revenue_range='$10M-$100M annual revenue',
                    geographic_presence='Domestic focus with expansion plans',
                    business_model='Innovative business model with scalable platform',
                    competitive_position='Emerging player with disruptive potential',
                    recent_initiatives=[
                        'Funding rounds and capital raising',
                        'Product development and innovation',
                        'Team scaling and talent acquisition',
                        'Market validation and customer acquisition'
                    ],
                    strategic_goals=[
                        'Rapid growth and scaling',
                        'Market validation and penetration',
                        'Product-market fit optimization',
                        'Funding and investment attraction',
                        'Competitive differentiation'
                    ]
                )
            ]
        }
    
    def _initialize_persona_variations(self) -> Dict[PersonaType, List[Dict[str, Any]]]:
        """Initialize persona variations for different contexts"""
        return {
            PersonaType.CLIENT: [
                {
                    'title': 'Chief Strategy Officer',
                    'focus_areas': ['Strategic planning', 'Market analysis', 'Competitive positioning'],
                    'decision_style': 'Strategic and long-term focused',
                    'key_metrics': ['Market share', 'Revenue growth', 'Strategic milestones'],
                    'typical_questions': [
                        'How does this align with our strategic objectives?',
                        'What are the competitive implications?',
                        'How will this position us for future growth?'
                    ]
                },
                {
                    'title': 'Chief Financial Officer',
                    'focus_areas': ['Financial performance', 'Risk management', 'Cost optimization'],
                    'decision_style': 'Data-driven and risk-conscious',
                    'key_metrics': ['ROI', 'NPV', 'Payback period', 'Risk-adjusted returns'],
                    'typical_questions': [
                        'What is the expected return on investment?',
                        'How do we mitigate financial risks?',
                        'What are the cash flow implications?'
                    ]
                },
                {
                    'title': 'Chief Technology Officer',
                    'focus_areas': ['Technology strategy', 'Innovation', 'Digital transformation'],
                    'decision_style': 'Innovation-focused and technically rigorous',
                    'key_metrics': ['Technology adoption', 'Innovation pipeline', 'System performance'],
                    'typical_questions': [
                        'How does this integrate with our technology stack?',
                        'What are the scalability considerations?',
                        'How will this drive innovation?'
                    ]
                }
            ],
            PersonaType.VENDOR: [
                {
                    'title': 'Sales Director',
                    'focus_areas': ['Revenue generation', 'Customer relationships', 'Market expansion'],
                    'decision_style': 'Relationship-focused and results-oriented',
                    'key_metrics': ['Sales targets', 'Customer satisfaction', 'Market penetration'],
                    'typical_questions': [
                        'How can we maximize value for both parties?',
                        'What are the implementation timelines?',
                        'How do we ensure customer success?'
                    ]
                },
                {
                    'title': 'Product Manager',
                    'focus_areas': ['Product development', 'Market fit', 'Customer needs'],
                    'decision_style': 'Customer-centric and analytical',
                    'key_metrics': ['Product adoption', 'Customer feedback', 'Feature utilization'],
                    'typical_questions': [
                        'How does this meet customer requirements?',
                        'What are the product roadmap implications?',
                        'How do we measure success?'
                    ]
                },
                {
                    'title': 'Partnership Director',
                    'focus_areas': ['Strategic alliances', 'Ecosystem development', 'Joint value creation'],
                    'decision_style': 'Collaborative and strategic',
                    'key_metrics': ['Partnership value', 'Ecosystem growth', 'Joint outcomes'],
                    'typical_questions': [
                        'How can we create mutual value?',
                        'What are the partnership governance structures?',
                        'How do we align incentives?'
                    ]
                }
            ],
            PersonaType.CONSULTANT: [
                {
                    'title': 'Strategy Consultant',
                    'focus_areas': ['Strategic analysis', 'Market insights', 'Transformation planning'],
                    'decision_style': 'Analytical and framework-driven',
                    'key_metrics': ['Strategic impact', 'Implementation success', 'Value realization'],
                    'typical_questions': [
                        'What is the strategic rationale?',
                        'How does this compare to best practices?',
                        'What are the implementation risks?'
                    ]
                },
                {
                    'title': 'Technology Consultant',
                    'focus_areas': ['Technology assessment', 'Digital transformation', 'System integration'],
                    'decision_style': 'Technical and methodical',
                    'key_metrics': ['Technical feasibility', 'Integration complexity', 'Performance improvement'],
                    'typical_questions': [
                        'What are the technical requirements?',
                        'How complex is the integration?',
                        'What are the performance implications?'
                    ]
                },
                {
                    'title': 'Change Management Consultant',
                    'focus_areas': ['Organizational change', 'Stakeholder engagement', 'Adoption strategies'],
                    'decision_style': 'People-focused and systematic',
                    'key_metrics': ['Adoption rates', 'Stakeholder satisfaction', 'Change readiness'],
                    'typical_questions': [
                        'How will this impact the organization?',
                        'What is the change management strategy?',
                        'How do we ensure stakeholder buy-in?'
                    ]
                }
            ]
        }
    
    def generate_dynamic_persona(self, persona_type: PersonaType, agenda: str, 
                                industry: str = 'energy') -> Dict[str, Any]:
        """Generate a dynamic persona with contextual background"""
        try:
            # Get base persona profile
            base_profiles = self.persona_variations.get(persona_type, [])
            if not base_profiles:
                raise ValueError(f"No profiles found for persona type: {persona_type}")
            
            # Select a random variation
            selected_profile = random.choice(base_profiles)
            
            # Get industry context
            industry_context = self.industry_contexts.get(industry, self.industry_contexts['energy'])
            
            # Select appropriate company profile
            company_size = random.choice(['large_enterprise', 'mid_market', 'growth_company'])
            company_profiles = self.company_profiles.get(company_size, [])
            company_profile = random.choice(company_profiles) if company_profiles else None
            
            # Generate agenda-specific context
            agenda_context = self._generate_agenda_context(agenda, industry_context)
            
            # Create comprehensive persona
            dynamic_persona = {
                'persona_type': persona_type.value,
                'title': selected_profile['title'],
                'industry_context': {
                    'industry': industry_context.industry,
                    'current_trends': random.sample(industry_context.current_trends, 3),
                    'key_challenges': random.sample(industry_context.key_challenges, 3),
                    'relevant_regulations': random.sample(industry_context.regulatory_environment, 2)
                },
                'company_context': {
                    'size': company_profile.company_size if company_profile else 'Mid-Market Company',
                    'revenue': company_profile.revenue_range if company_profile else '$100M-$1B',
                    'presence': company_profile.geographic_presence if company_profile else 'Regional',
                    'recent_initiatives': company_profile.recent_initiatives[:2] if company_profile else [],
                    'strategic_goals': company_profile.strategic_goals[:3] if company_profile else []
                },
                'role_context': {
                    'focus_areas': selected_profile['focus_areas'],
                    'decision_style': selected_profile['decision_style'],
                    'key_metrics': selected_profile['key_metrics'],
                    'typical_questions': selected_profile['typical_questions']
                },
                'agenda_context': agenda_context,
                'background_story': self._generate_background_story(
                    selected_profile, industry_context, agenda
                )
            }
            
            return dynamic_persona
            
        except Exception as e:
            logger.error(f"Error generating dynamic persona: {e}")
            return {'error': f'Failed to generate persona: {e}'}
    
    def _generate_agenda_context(self, agenda: str, industry_context: IndustryContext) -> Dict[str, Any]:
        """Generate context specific to the meeting agenda"""
        agenda_lower = agenda.lower()
        
        # Identify agenda themes
        themes = []
        if any(word in agenda_lower for word in ['energy', 'power', 'grid', 'renewable']):
            themes.append('energy_transition')
        if any(word in agenda_lower for word in ['technology', 'digital', 'ai', 'automation']):
            themes.append('technology_adoption')
        if any(word in agenda_lower for word in ['risk', 'compliance', 'regulation']):
            themes.append('risk_management')
        if any(word in agenda_lower for word in ['cost', 'efficiency', 'optimization']):
            themes.append('cost_optimization')
        
        # Generate relevant context based on themes
        relevant_trends = []
        relevant_challenges = []
        
        for theme in themes:
            if theme == 'energy_transition':
                relevant_trends.extend(['Renewable energy integration', 'Grid modernization'])
                relevant_challenges.extend(['Infrastructure aging', 'Regulatory compliance'])
            elif theme == 'technology_adoption':
                relevant_trends.extend(['AI and machine learning', 'Digital transformation'])
                relevant_challenges.extend(['Legacy system integration', 'Cybersecurity'])
            elif theme == 'risk_management':
                relevant_trends.extend(['Advanced analytics', 'Predictive maintenance'])
                relevant_challenges.extend(['Market volatility', 'Operational risk'])
            elif theme == 'cost_optimization':
                relevant_trends.extend(['Process automation', 'Efficiency improvement'])
                relevant_challenges.extend(['Cost pressure', 'Resource optimization'])
        
        return {
            'agenda': agenda,
            'identified_themes': themes,
            'relevant_trends': list(set(relevant_trends)),
            'relevant_challenges': list(set(relevant_challenges)),
            'priority_level': 'High' if len(themes) > 2 else 'Medium',
            'complexity_level': 'Complex' if len(themes) > 1 else 'Straightforward'
        }
    
    def _generate_background_story(self, profile: Dict[str, Any], 
                                 industry_context: IndustryContext, agenda: str) -> str:
        """Generate a realistic background story for the persona"""
        stories = [
            f"As a {profile['title']}, I've been leading {profile['focus_areas'][0].lower()} "
            f"initiatives in the {industry_context.industry.lower()} sector. "
            f"Recently, our organization has been focusing on {random.choice(industry_context.current_trends).lower()}, "
            f"which is why today's discussion about '{agenda}' is particularly relevant to our strategic priorities.",
            
            f"In my role as {profile['title']}, I'm responsible for {profile['focus_areas'][1].lower()} "
            f"across our organization. We're currently facing challenges with {random.choice(industry_context.key_challenges).lower()}, "
            f"and I'm interested in understanding how '{agenda}' might help address these issues.",
            
            f"Our company has been navigating {random.choice(industry_context.current_trends).lower()} "
            f"over the past year, and as {profile['title']}, I'm evaluating various approaches. "
            f"Today's agenda on '{agenda}' aligns with our need to {profile['focus_areas'][0].lower()} "
            f"while managing the complexities of our industry."
        ]
        
        return random.choice(stories)
