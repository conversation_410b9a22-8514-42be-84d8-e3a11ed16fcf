#!/usr/bin/env python3
"""
Fast Market Price Analysis Chatbot
"""

from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import os
import logging
from dotenv import load_dotenv
from services.fast_nlp_service import FastMarketAnalyzer

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Initialize fast analyzer
fast_analyzer = FastMarketAnalyzer()

@app.route('/')
def index():
    """Serve the main chat interface"""
    return render_template('index.html')

@app.route('/api/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Fast Market Price Analysis Chatbot',
        'version': '2.0'
    })

@app.route('/api/chat', methods=['POST'])
def chat():
    """Process chat messages with fast analysis"""
    try:
        user_message = request.json.get('message', '')
        
        if not user_message:
            return jsonify({
                'error': 'No message provided'
            }), 400
        
        logger.info(f"Processing query: {user_message}")
        
        # Step 1: Parse query using fast rule-based approach
        query_params = fast_analyzer.parse_query(user_message)
        logger.info(f"Parsed query: {query_params}")
        
        if query_params.get('confidence', 0) < 0.3:
            return jsonify({
                'response': 'Could not understand the query. Please try rephrasing.',
                'suggestions': [
                    'Try: "ERCOT LMP statistics this week"',
                    'Try: "NDDAM price volatility for PJM"',
                    'Try: "What is the RMSE for CAISO real-time prices?"'
                ],
                'confidence': query_params.get('confidence', 0)
            })
        
        # Step 2: Get available sensors
        available_sensors = fast_analyzer.get_mock_sensors()
        
        # Step 3: Find best matching sensor
        best_sensor = fast_analyzer.find_best_sensor(query_params, available_sensors)
        
        if not best_sensor:
            # Create a mock sensor based on the query
            iso = query_params.get('iso', 'UNKNOWN').upper()
            data_type = query_params.get('data_type', 'lmp').upper()
            hub = query_params.get('hub_or_node', 'HUB').upper()
            
            best_sensor = {
                'sensor_id': f'{iso}_{data_type}_{hub}_MOCK',
                'description': f'{iso} {hub} {data_type} Mock Sensor for Demonstration',
                'report_region': iso,
                'report_name': f'{data_type} Mock Report'
            }
        
        # Step 4: Generate mock data
        market_data = fast_analyzer.generate_mock_data(query_params)
        
        # Step 5: Perform analysis
        analysis_type = query_params.get('analysis_type', 'statistics')
        analysis_result = fast_analyzer.perform_analysis(market_data, analysis_type)
        
        if 'error' in analysis_result:
            return jsonify({
                'response': f'Analysis failed: {analysis_result["error"]}',
                'query_params': query_params,
                'sensor_used': best_sensor
            })
        
        # Step 6: Generate natural language response
        response = generate_response(query_params, analysis_result, best_sensor, market_data)
        
        return jsonify({
            'response': response,
            'query_params': query_params,
            'sensor_used': best_sensor,
            'data_summary': {
                'data_points': len(market_data),
                'date_range': f"{market_data['timestamp'].min().date()} to {market_data['timestamp'].max().date()}",
                'price_range': f"${market_data['price'].min():.2f} - ${market_data['price'].max():.2f}/MWh",
                'sensor_description': best_sensor.get('description', '')
            },
            'analysis_result': analysis_result,
            'confidence': query_params.get('confidence', 0)
        })
        
    except Exception as e:
        logger.error(f"Error processing chat request: {str(e)}")
        return jsonify({
            'error': f'Failed to process request: {str(e)}'
        }), 500

def generate_response(query_params, analysis_result, sensor_info, market_data):
    """Generate natural language response"""
    try:
        # Extract key information
        iso = query_params.get('iso', 'Unknown').upper()
        data_type = query_params.get('data_type', 'market data').upper()
        analysis_type = query_params.get('analysis_type', 'analysis')
        sensor_description = sensor_info.get('description', 'market sensor')
        
        # Start building response
        response_parts = []
        
        # Header with query understanding
        response_parts.append(f"📊 **Analysis Results for {iso} {data_type}**")
        response_parts.append(f"*Based on: {sensor_description}*")
        response_parts.append("")
        
        # Add analysis results dynamically
        if isinstance(analysis_result, dict):
            for key, value in analysis_result.items():
                if key == 'error':
                    continue
                
                # Format different types of metrics
                if 'rmse' in key.lower():
                    response_parts.append(f"🎯 **Root Mean Square Error**: {value:.3f} $/MWh")
                elif 'mae' in key.lower():
                    response_parts.append(f"📏 **Mean Absolute Error**: {value:.3f} $/MWh")
                elif 'mape' in key.lower():
                    response_parts.append(f"📊 **Mean Absolute Percentage Error**: {value:.2f}%")
                elif 'volatility' in key.lower():
                    response_parts.append(f"📈 **Volatility**: {value:.2f}%")
                elif 'mean' in key.lower() or 'average' in key.lower():
                    response_parts.append(f"💰 **Average Price**: ${value:.2f}/MWh")
                elif 'std' in key.lower() or 'deviation' in key.lower():
                    response_parts.append(f"📊 **Standard Deviation**: ${value:.2f}/MWh")
                elif 'max' in key.lower():
                    response_parts.append(f"⬆️ **Maximum**: ${value:.2f}/MWh")
                elif 'min' in key.lower():
                    response_parts.append(f"⬇️ **Minimum**: ${value:.2f}/MWh")
                elif 'premium' in key.lower():
                    if 'percentage' in key.lower():
                        response_parts.append(f"📈 **Peak Premium**: {value:.1f}%")
                    else:
                        response_parts.append(f"💰 **Peak Premium**: ${value:.2f}/MWh")
                elif 'peak_average' in key.lower():
                    response_parts.append(f"🌅 **Peak Hours Average**: ${value:.2f}/MWh")
                elif 'off_peak_average' in key.lower():
                    response_parts.append(f"🌙 **Off-Peak Average**: ${value:.2f}/MWh")
                elif 'median' in key.lower():
                    response_parts.append(f"📊 **Median**: ${value:.2f}/MWh")
                elif 'count' in key.lower() or 'points' in key.lower():
                    response_parts.append(f"📊 **Data Points**: {value:,}")
                else:
                    # Generic formatting for other metrics
                    if isinstance(value, (int, float)):
                        if abs(value) < 1:
                            response_parts.append(f"📊 **{key.replace('_', ' ').title()}**: {value:.4f}")
                        elif abs(value) < 100:
                            response_parts.append(f"📊 **{key.replace('_', ' ').title()}**: {value:.2f}")
                        else:
                            response_parts.append(f"📊 **{key.replace('_', ' ').title()}**: {value:,.0f}")
                    else:
                        response_parts.append(f"📊 **{key.replace('_', ' ').title()}**: {value}")
        
        # Add summary information
        response_parts.append("")
        response_parts.append(f"📅 **Analysis Period**: {query_params.get('time_period', 'this week')}")
        response_parts.append(f"📊 **Data Points**: {len(market_data):,} hourly observations")
        response_parts.append(f"⚡ **Powered by**: Fast Rule-Based Analysis Engine")
        
        # Add insights based on analysis type
        if analysis_type == 'volatility':
            daily_vol = analysis_result.get('daily_volatility', 0)
            if daily_vol > 20:
                response_parts.append(f"⚠️ **High volatility detected** - Market shows significant price swings")
            elif daily_vol < 5:
                response_parts.append(f"✅ **Low volatility** - Market prices are relatively stable")
            else:
                response_parts.append(f"📊 **Moderate volatility** - Normal market price variation")
        
        elif analysis_type == 'peak_analysis':
            premium_pct = analysis_result.get('peak_premium_percentage', 0)
            if premium_pct > 50:
                response_parts.append(f"⚠️ **High peak premium** - Significant price difference between peak and off-peak")
            elif premium_pct < 10:
                response_parts.append(f"📊 **Low peak premium** - Minimal price difference between peak and off-peak")
            else:
                response_parts.append(f"📊 **Moderate peak premium** - Normal peak/off-peak price differential")
        
        return "\n".join(response_parts)
        
    except Exception as e:
        logger.error(f"Error generating response: {str(e)}")
        return f"Analysis completed but failed to generate detailed response. Raw results: {analysis_result}"

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5001))
    debug = os.environ.get('DEBUG', 'True').lower() == 'true'
    
    print(f"🚀 Starting Fast Market Price Analysis Chatbot")
    print(f"🌐 Server will run on http://127.0.0.1:{port}")
    print(f"🤖 Fast rule-based analysis engine ready")
    print(f"📊 Mock data generation enabled")
    
    app.run(host='0.0.0.0', port=port, debug=debug)
