#!/usr/bin/env python3
"""
Demo test to show the chatbot functionality
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.nlp_service import NLPProcessor
from services.api_service import MarketDataAPI
from services.analysis_service import DataAnalyzer
from services.response_generator import ResponseGenerator

def demo_chatbot():
    """Demonstrate the chatbot with various queries"""
    print("🤖 MARKET PRICE ANALYSIS CHATBOT DEMO")
    print("=" * 60)
    print("This demo shows how the chatbot processes natural language")
    print("queries and generates intelligent responses about market data.")
    print("=" * 60)
    
    # Initialize services
    nlp = NLPProcessor()
    api = MarketDataAPI()
    analyzer = DataAnalyzer()
    response_gen = ResponseGenerator()
    
    # Demo queries
    demo_queries = [
        "NDDAM price of ERCOT this week",
        "What is the RMSE for ERCOT LMP North Hub?",
        "Show me volatility analysis for PJM",
        "Peak hours analysis for CAISO last week",
        "Real-time price statistics for NYISO"
    ]
    
    for i, query in enumerate(demo_queries, 1):
        print(f"\n🔍 DEMO {i}: Processing '{query}'")
        print("-" * 50)
        
        # Step 1: Parse the natural language query
        print("1️⃣ Parsing natural language query...")
        query_params = nlp.parse_query(query)
        
        if not query_params:
            print("   ❌ Could not parse query")
            continue
        
        print(f"   ✅ Extracted: ISO={query_params['iso']}, DataType={query_params['data_type']}")
        print(f"      Hub={query_params.get('hub', 'None')}, Analysis={query_params['analysis_type']}")
        print(f"      Date Range: {query_params['start_date']} to {query_params['end_date']}")
        
        # Step 2: Fetch market data (using mock data for demo)
        print("\n2️⃣ Fetching market data...")
        data = api.fetch_data(
            iso=query_params['iso'],
            hub=query_params.get('hub'),
            data_type=query_params['data_type'],
            start_date=query_params['start_date'],
            end_date=query_params['end_date']
        )
        
        if data is None or data.empty:
            print("   ❌ No data available")
            continue
        
        print(f"   ✅ Fetched {len(data)} data points")
        print(f"      Price range: ${data['price'].min():.2f} - ${data['price'].max():.2f}/MWh")
        print(f"      Time period: {data['timestamp'].min().date()} to {data['timestamp'].max().date()}")
        
        # Step 3: Perform analysis
        print("\n3️⃣ Performing statistical analysis...")
        analysis_result = analyzer.analyze(data, query_params['analysis_type'])
        
        if 'error' in analysis_result:
            print(f"   ❌ Analysis error: {analysis_result['error']}")
            continue
        
        print("   ✅ Analysis completed successfully")
        
        # Show key metrics based on analysis type
        if query_params['analysis_type'] == 'rmse':
            print(f"      RMSE: {analysis_result.get('rmse', 0):.3f} $/MWh")
            print(f"      MAE: {analysis_result.get('mae', 0):.3f} $/MWh")
        elif query_params['analysis_type'] == 'volatility':
            print(f"      Daily Volatility: {analysis_result.get('daily_volatility', 0):.2f}%")
            print(f"      Annualized Volatility: {analysis_result.get('annualized_volatility', 0):.1f}%")
        elif query_params['analysis_type'] == 'statistics':
            print(f"      Mean Price: ${analysis_result.get('mean', 0):.2f}/MWh")
            print(f"      Std Dev: ${analysis_result.get('std_dev', 0):.2f}/MWh")
        elif query_params['analysis_type'] == 'peak_analysis':
            print(f"      Peak Premium: ${analysis_result.get('peak_premium', 0):.2f}/MWh")
            print(f"      Peak Premium %: {analysis_result.get('peak_premium_percentage', 0):.1f}%")
        
        # Step 4: Generate natural language response
        print("\n4️⃣ Generating natural language response...")
        response = response_gen.generate_response(query_params, analysis_result, query)
        
        print("   ✅ Response generated")
        print("\n📊 CHATBOT RESPONSE:")
        print("   " + "="*45)
        
        # Show first few lines of the response
        response_lines = response['text'].split('\n')
        for line in response_lines[:8]:  # Show first 8 lines
            if line.strip():
                print(f"   {line}")
        
        if len(response_lines) > 8:
            print("   ...")
        
        print("   " + "="*45)
        
        # Show data summary
        if response.get('data_summary'):
            print(f"\n📈 Data Summary: {response['data_summary']['data_points']} points from {response['data_summary']['iso']}")

def test_web_api():
    """Test the web API endpoint"""
    print("\n\n🌐 WEB API TEST")
    print("=" * 60)
    
    import requests
    
    try:
        # Test health endpoint
        print("Testing health endpoint...")
        health_response = requests.get('http://127.0.0.1:5000/api/health', timeout=5)
        if health_response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Response: {health_response.json()}")
        else:
            print(f"❌ Health check failed: {health_response.status_code}")
        
        # Test chat endpoint
        print("\nTesting chat endpoint...")
        chat_data = {
            "message": "ERCOT LMP statistics this week"
        }
        
        chat_response = requests.post(
            'http://127.0.0.1:5000/api/chat',
            json=chat_data,
            timeout=15
        )
        
        if chat_response.status_code == 200:
            print("✅ Chat API working")
            response_data = chat_response.json()
            print(f"   Response preview: {response_data['response'][:100]}...")
            if response_data.get('data_summary'):
                print(f"   Data points: {response_data['data_summary'].get('data_points')}")
        else:
            print(f"❌ Chat API failed: {chat_response.status_code}")
            print(f"   Error: {chat_response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to web server")
        print("   Make sure the Flask app is running: python app.py")
    except Exception as e:
        print(f"❌ Web API test failed: {str(e)}")

def main():
    """Run the demo"""
    print(f"Demo started at: {datetime.now()}")
    
    try:
        # Run the chatbot demo
        demo_chatbot()
        
        # Test the web API
        test_web_api()
        
        print("\n\n🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("The chatbot is working perfectly with:")
        print("✅ Natural language processing")
        print("✅ Market data fetching (mock data)")
        print("✅ Statistical analysis")
        print("✅ Intelligent response generation")
        print("✅ Web interface")
        print("\nTo use with real Genscape data:")
        print("1. Verify API credentials and endpoints")
        print("2. Update authentication method if needed")
        print("3. Test with actual sensor IDs")
        print("\nWeb interface available at: http://127.0.0.1:5000")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
