import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import os
import logging
import re
from dotenv import load_dotenv
from .ai_nlp_service import AIMarketAnalyzer

load_dotenv()
logger = logging.getLogger(__name__)

class MarketDataAPI:
    """Service for fetching market price data from Genscape APIs"""

    def __init__(self):
        # API Configuration
        self.market_price_base_url = os.getenv('MARKET_PRICE_API_URL', 'https://api.genscape.com/marketintelligence/na')
        self.metadata_base_url = os.getenv('METADATA_API_URL', 'https://api.genscape.com/marketintelligence/na')
        self.api_key = os.getenv('GENSCAPE_API_KEY', '')
        self.timeout = 30

        # Initialize AI analyzer
        self.ai_analyzer = AIMarketAnalyzer()

        if not self.api_key:
            logger.warning("No Genscape API key found. Using mock data.")

        # Cache for sensor metadata to avoid repeated API calls
        self._sensor_cache = {}
        self._region_cache = {}

        # Common search terms and mappings
        self.iso_search_terms = {
            'ercot': ['ercot', 'texas', 'electric reliability council'],
            'pjm': ['pjm', 'pennsylvania', 'new jersey', 'maryland'],
            'caiso': ['caiso', 'california', 'california iso'],
            'nyiso': ['nyiso', 'new york', 'new york iso'],
            'iso-ne': ['iso-ne', 'isone', 'new england', 'iso new england'],
            'miso': ['miso', 'midcontinent', 'midwest'],
            'spp': ['spp', 'southwest power pool']
        }

        # Common price/metric terms
        self.price_terms = {
            'lmp': ['lmp', 'locational marginal price', 'marginal price'],
            'da': ['da', 'day ahead', 'day-ahead'],
            'rt': ['rt', 'real time', 'real-time'],
            'nddam': ['nddam', 'next day day ahead market'],
            'load': ['load', 'demand'],
            'generation': ['generation', 'gen'],
            'congestion': ['congestion', 'cong'],
            'loss': ['loss', 'losses']
        }

        # Hub/Node search terms
        self.hub_terms = {
            'north': ['north', 'northern'],
            'south': ['south', 'southern'],
            'west': ['west', 'western'],
            'east': ['east', 'eastern'],
            'houston': ['houston'],
            'hub': ['hub'],
            'zone': ['zone'],
            'node': ['node']
        }
    
    def _get_headers(self) -> Dict[str, str]:
        """Get API request headers"""
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        if self.api_key:
            # Try different authentication methods
            headers['Authorization'] = f'Bearer {self.api_key}'
            headers['X-API-Key'] = self.api_key
            headers['apikey'] = self.api_key
        return headers

    def search_sensors(self, query_terms: List[str], region: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Search for sensors using the metadata API

        Args:
            query_terms: List of search terms to look for
            region: Optional region filter
            limit: Maximum number of results

        Returns:
            List of matching sensors with metadata
        """
        try:
            # Build search URL
            url = f"{self.metadata_base_url}/v1/searchreportmetadata"
            params = {'limit': limit}

            if region:
                params['region'] = region

            logger.info(f"Searching sensors with terms: {query_terms}, region: {region}")

            response = requests.get(
                url,
                params=params,
                headers=self._get_headers(),
                timeout=self.timeout
            )

            if response.status_code == 200:
                data = response.json()

                # Filter results based on query terms
                matching_sensors = []

                for report in data.get('reports', []):
                    report_name = report.get('reportName', '').lower()
                    report_region = report.get('reportRegion', '').lower()

                    # Check if any query terms match the report
                    if self._matches_query_terms(report_name + ' ' + report_region, query_terms):
                        for sid_info in report.get('sids', []):
                            sensor_data = {
                                'sensor_id': sid_info.get('id'),
                                'description': sid_info.get('description', ''),
                                'report_name': report.get('reportName'),
                                'report_region': report.get('reportRegion'),
                                'report_id': report.get('reportId')
                            }

                            # Additional filtering on sensor description
                            if self._matches_query_terms(sensor_data['description'].lower(), query_terms):
                                matching_sensors.append(sensor_data)

                logger.info(f"Found {len(matching_sensors)} matching sensors")
                return matching_sensors

            else:
                logger.error(f"Metadata API request failed with status {response.status_code}: {response.text}")
                return []

        except Exception as e:
            logger.error(f"Error searching sensors: {str(e)}")
            return []

    def _matches_query_terms(self, text: str, query_terms: List[str]) -> bool:
        """Check if text matches any of the query terms"""
        text_lower = text.lower()
        return any(term.lower() in text_lower for term in query_terms if term)

    def find_sensor_for_query(self, iso: str, metric_type: str, hub: str = None) -> Optional[str]:
        """
        Find the best sensor ID for a given query

        Args:
            iso: ISO name (e.g., 'ercot', 'pjm')
            metric_type: Type of metric (e.g., 'lmp', 'nddam', 'load')
            hub: Optional hub/node name

        Returns:
            Sensor ID if found, None otherwise
        """
        # Build search terms
        search_terms = []

        # Add ISO terms
        if iso and iso.lower() in self.iso_search_terms:
            search_terms.extend(self.iso_search_terms[iso.lower()])

        # Add metric terms
        if metric_type and metric_type.lower() in self.price_terms:
            search_terms.extend(self.price_terms[metric_type.lower()])

        # Add hub terms
        if hub:
            hub_lower = hub.lower()
            for hub_key, hub_values in self.hub_terms.items():
                if any(term in hub_lower for term in hub_values):
                    search_terms.extend(hub_values)
                    break

        # Search for sensors
        sensors = self.search_sensors(search_terms)

        if not sensors:
            logger.warning(f"No sensors found for {iso} {metric_type} {hub}")
            return None

        # Score and rank sensors based on relevance
        scored_sensors = []
        for sensor in sensors:
            score = self._score_sensor_relevance(sensor, iso, metric_type, hub)
            scored_sensors.append((sensor, score))

        # Sort by score (highest first)
        scored_sensors.sort(key=lambda x: x[1], reverse=True)

        if scored_sensors:
            best_sensor = scored_sensors[0][0]
            logger.info(f"Selected sensor: {best_sensor['sensor_id']} - {best_sensor['description']}")
            return best_sensor['sensor_id']

        return None

    def _score_sensor_relevance(self, sensor: Dict[str, Any], iso: str, metric_type: str, hub: str = None) -> float:
        """Score sensor relevance based on query parameters"""
        score = 0.0
        description = sensor.get('description', '').lower()
        report_name = sensor.get('report_name', '').lower()

        # ISO matching
        if iso and iso.lower() in self.iso_search_terms:
            iso_terms = self.iso_search_terms[iso.lower()]
            if any(term in description or term in report_name for term in iso_terms):
                score += 3.0

        # Metric type matching
        if metric_type and metric_type.lower() in self.price_terms:
            metric_terms = self.price_terms[metric_type.lower()]
            if any(term in description for term in metric_terms):
                score += 2.0

        # Hub matching
        if hub:
            hub_lower = hub.lower()
            if hub_lower in description:
                score += 1.5

            # Check for hub type matches
            for hub_key, hub_values in self.hub_terms.items():
                if any(term in hub_lower for term in hub_values):
                    if any(term in description for term in hub_values):
                        score += 1.0
                    break

        # Prefer more specific descriptions (longer descriptions often more specific)
        score += len(description) * 0.001

        return score
    
    def fetch_market_data(self, sensor_id: str, start_date: str, end_date: str, interval: str = 'hourly') -> Optional[pd.DataFrame]:
        """
        Fetch market data using the Genscape Market Price API

        Args:
            sensor_id: Sensor ID to fetch data for
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            interval: Data interval ('hourly', 'daily', etc.)

        Returns:
            DataFrame with market data or None if error
        """
        try:
            # Build API endpoint
            endpoint = f"{self.market_price_base_url}/v1/getepcalcsiddata"

            # Build query parameters
            params = {
                'sids': sensor_id,
                'start_date': start_date,
                'end_date': end_date,
                'interval': interval
            }

            logger.info(f"Fetching data from {endpoint} with params: {params}")

            # Make API request
            response = requests.get(
                endpoint,
                params=params,
                headers=self._get_headers(),
                timeout=self.timeout
            )

            if response.status_code == 200:
                data = response.json()

                # Convert to DataFrame
                if data and isinstance(data, list):
                    df = pd.DataFrame(data)

                    # Rename columns to match our expected format
                    column_mapping = {
                        'SensorID': 'sensor_id',
                        'Value': 'price',
                        'date': 'timestamp'
                    }

                    df = df.rename(columns=column_mapping)

                    # Ensure datetime column
                    if 'timestamp' in df.columns:
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        df = df.sort_values('timestamp')

                    # Add metadata
                    df['unit'] = 'USD/MWh'  # Assuming price data

                    logger.info(f"Successfully fetched {len(df)} records for sensor {sensor_id}")
                    return df
                else:
                    logger.warning(f"No data returned from API for sensor {sensor_id}")
                    return None

            elif response.status_code == 404:
                logger.warning(f"Data not found for sensor {sensor_id}")
                return None

            else:
                logger.error(f"API request failed with status {response.status_code}: {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Network error fetching data: {str(e)}")
            return self._get_mock_data_for_sensor(sensor_id, start_date, end_date)

        except Exception as e:
            logger.error(f"Error fetching data: {str(e)}")
            return self._get_mock_data_for_sensor(sensor_id, start_date, end_date)

    def fetch_data(self, iso: str, hub: str = None, start_date: str = None,
                   end_date: str = None, data_type: str = 'lmp') -> Optional[pd.DataFrame]:
        """
        High-level method to fetch data by finding appropriate sensor and getting data

        Args:
            iso: ISO name (e.g., 'ercot', 'pjm')
            hub: Hub name (optional)
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            data_type: Type of data ('lmp', 'nddam', 'load', etc.)

        Returns:
            DataFrame with market data or None if error
        """
        try:
            # Set default date range if not provided (last 7 days)
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            if not start_date:
                start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

            # Find appropriate sensor
            sensor_id = self.find_sensor_for_query(iso, data_type, hub)

            if not sensor_id:
                logger.warning(f"Could not find sensor for {iso} {data_type} {hub}")
                return self._get_mock_data(iso, hub, start_date, end_date)

            # Fetch data using the sensor
            return self.fetch_market_data(sensor_id, start_date, end_date)

        except Exception as e:
            logger.error(f"Error in fetch_data: {str(e)}")
            return self._get_mock_data(iso, hub, start_date, end_date)
    
    def _get_mock_data_for_sensor(self, sensor_id: str, start_date: str = None,
                                  end_date: str = None) -> pd.DataFrame:
        """Generate mock data for a specific sensor ID"""
        logger.info(f"Generating mock data for sensor {sensor_id}")

        # Set default date range
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

        # Generate date range
        date_range = pd.date_range(start=start_date, end=end_date, freq='H')

        # Generate realistic price data
        import numpy as np
        np.random.seed(hash(sensor_id) % 2**32)  # Consistent seed based on sensor ID

        base_price = 50.0
        prices = []

        for i, timestamp in enumerate(date_range):
            hour = timestamp.hour
            day_of_week = timestamp.weekday()

            # Peak hours pattern
            if 7 <= hour <= 22:
                price_multiplier = 1.2
            else:
                price_multiplier = 0.8

            # Weekday vs weekend
            if day_of_week < 5:
                price_multiplier *= 1.1

            # Add volatility
            volatility = np.random.normal(0, 0.1)
            price = base_price * price_multiplier * (1 + volatility)
            prices.append(max(price, 10.0))

        return pd.DataFrame({
            'timestamp': date_range,
            'price': prices,
            'sensor_id': sensor_id,
            'unit': 'USD/MWh'
        })

    def _get_mock_data(self, iso: str, hub: str = None, start_date: str = None,
                       end_date: str = None) -> pd.DataFrame:
        """
        Generate mock data for testing when API is not available
        """
        logger.info("Generating mock data for testing")
        
        # Set default date range
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        # Generate date range
        date_range = pd.date_range(start=start_date, end=end_date, freq='H')
        
        # Generate realistic price data with some volatility
        import numpy as np
        np.random.seed(42)  # For reproducible results
        
        base_price = 50.0  # Base price in $/MWh
        prices = []
        
        for i, timestamp in enumerate(date_range):
            # Add daily and weekly patterns
            hour = timestamp.hour
            day_of_week = timestamp.weekday()
            
            # Peak hours (higher prices)
            if 7 <= hour <= 22:
                price_multiplier = 1.2
            else:
                price_multiplier = 0.8
            
            # Weekday vs weekend
            if day_of_week < 5:  # Weekday
                price_multiplier *= 1.1
            
            # Add random volatility
            volatility = np.random.normal(0, 0.1)
            
            price = base_price * price_multiplier * (1 + volatility)
            prices.append(max(price, 10.0))  # Minimum price floor
        
        # Create DataFrame
        df = pd.DataFrame({
            'timestamp': date_range,
            'price': prices,
            'iso': iso.upper(),
            'hub': hub.upper() if hub else 'DEFAULT_HUB',
            'unit': 'USD/MWh'
        })
        
        return df
    
    def get_available_regions(self) -> List[str]:
        """Get list of available regions from metadata API"""
        try:
            # Search metadata to get available regions
            sensors = self.search_sensors([''], limit=100)
            regions = set()

            for sensor in sensors:
                region = sensor.get('report_region')
                if region:
                    regions.add(region)

            return sorted(list(regions))

        except Exception as e:
            logger.error(f"Error fetching regions: {str(e)}")
            return ['ERCOT', 'PJM', 'CAISO', 'NYISO', 'ISONE', 'MISO', 'SPP']

    def get_available_metrics(self, iso: str = None) -> List[Dict[str, str]]:
        """Get list of available metrics/sensors for an ISO"""
        try:
            search_terms = []
            if iso and iso.lower() in self.iso_search_terms:
                search_terms = self.iso_search_terms[iso.lower()]

            sensors = self.search_sensors(search_terms, limit=50)

            # Format for display
            metrics = []
            for sensor in sensors:
                metrics.append({
                    'id': sensor['sensor_id'],
                    'description': sensor['description'],
                    'region': sensor['report_region']
                })

            return metrics

        except Exception as e:
            logger.error(f"Error fetching metrics for {iso}: {str(e)}")
            return []

    def search_by_description(self, description_query: str) -> List[Dict[str, Any]]:
        """Search sensors by description text"""
        try:
            # Split query into terms
            query_terms = description_query.lower().split()

            # Search sensors
            sensors = self.search_sensors(query_terms, limit=20)

            return sensors

        except Exception as e:
            logger.error(f"Error searching by description: {str(e)}")
            return []

    def process_ai_query(self, user_query: str) -> Dict[str, Any]:
        """
        Process natural language query using AI to understand intent and fetch data

        Args:
            user_query: Natural language query from user

        Returns:
            Dictionary containing parsed parameters, data, and analysis results
        """
        try:
            logger.info(f"Processing AI query: {user_query}")

            # Step 1: Create mock sensors for demonstration (since API auth is not working)
            available_sensors = self._get_mock_sensors()

            # Step 2: Use AI to parse the natural language query
            query_params = self.ai_analyzer.parse_natural_language_query(user_query, available_sensors)
            logger.info(f"AI parsed query: {query_params}")

            if not query_params or query_params.get('confidence', 0) < 0.3:
                return {
                    'error': 'Could not understand the query. Please try rephrasing.',
                    'suggestions': [
                        'Try: "ERCOT LMP statistics this week"',
                        'Try: "NDDAM price volatility for PJM"',
                        'Try: "What is the RMSE for CAISO real-time prices?"'
                    ]
                }

            # Step 3: Find best matching sensors using AI
            best_sensors = self.ai_analyzer.find_best_sensors(query_params, available_sensors)

            if not best_sensors:
                # Create a mock sensor based on the query
                mock_sensor = self._create_mock_sensor_from_query(query_params)
                best_sensors = [mock_sensor]

            # Step 4: Generate mock data (since real API is not working)
            sensor_id = best_sensors[0].get('sensor_id')
            logger.info(f"Using sensor: {sensor_id} - {best_sensors[0].get('description', '')[:100]}")

            data = self._generate_mock_data(query_params)

            if data is None or data.empty:
                return {
                    'error': 'No data available for the specified parameters',
                    'query_params': query_params,
                    'sensor_used': best_sensors[0]
                }

            # Step 5: Perform traditional analysis (AI analysis times out)
            analysis_result = self._perform_traditional_analysis(data, query_params)

            return {
                'success': True,
                'query_params': query_params,
                'sensor_used': best_sensors[0],
                'data_summary': {
                    'data_points': len(data),
                    'date_range': f"{data['timestamp'].min().date()} to {data['timestamp'].max().date()}",
                    'price_range': f"${data['price'].min():.2f} - ${data['price'].max():.2f}/MWh",
                    'sensor_description': best_sensors[0].get('description', '')
                },
                'analysis_result': analysis_result,
                'raw_data': data.to_dict('records') if len(data) <= 100 else None  # Limit data size
            }

        except Exception as e:
            logger.error(f"Error in AI query processing: {str(e)}")
            return {
                'error': f'Failed to process query: {str(e)}',
                'fallback': 'Please try a simpler query or check your parameters'
            }

    def get_all_available_sensors(self, limit: int = 1000) -> List[Dict]:
        """
        Get all available sensors from metadata API for AI context

        Args:
            limit: Maximum number of sensors to return

        Returns:
            List of sensor dictionaries
        """
        try:
            # Try to get from cache first
            cache_key = f"all_sensors_{limit}"
            if cache_key in self._sensor_cache:
                cache_time, cached_data = self._sensor_cache[cache_key]
                if datetime.now().timestamp() - cache_time < 3600:  # 1 hour cache
                    return cached_data

            # Fetch from API
            url = f"{self.metadata_base_url}/v1/searchreportmetadata"
            headers = self._get_headers()
            params = {'limit': limit}

            response = requests.get(url, headers=headers, params=params, timeout=self.timeout)

            if response.status_code == 200:
                data = response.json()
                sensors = []

                # Extract sensors from reports
                for report in data.get('reports', []):
                    for sid_info in report.get('sids', []):
                        sensors.append({
                            'sensor_id': sid_info.get('id'),
                            'description': sid_info.get('description', ''),
                            'report_name': report.get('reportName', ''),
                            'report_region': report.get('reportRegion', ''),
                            'report_id': report.get('reportId', '')
                        })

                # Cache the results
                self._sensor_cache[cache_key] = (datetime.now().timestamp(), sensors)
                logger.info(f"Fetched {len(sensors)} sensors from metadata API")
                return sensors

            else:
                logger.error(f"Metadata API request failed with status {response.status_code}: {response.text}")
                return []

        except Exception as e:
            logger.error(f"Error fetching all sensors: {str(e)}")
            return []

    def _get_mock_sensors(self) -> List[Dict]:
        """Get mock sensors for demonstration"""
        return [
            {
                'sensor_id': 'ERCOT_LMP_NORTH_HUB_RT',
                'description': 'ERCOT North Hub Real-Time Locational Marginal Price',
                'report_region': 'ERCOT',
                'report_name': 'Real-Time LMP Report'
            },
            {
                'sensor_id': 'ERCOT_NDDAM_SOUTH_HUB',
                'description': 'ERCOT South Hub Next Day Day-Ahead Market Price',
                'report_region': 'ERCOT',
                'report_name': 'Day-Ahead Market Report'
            },
            {
                'sensor_id': 'PJM_LMP_WEST_HUB_DA',
                'description': 'PJM Western Hub Day-Ahead Locational Marginal Price',
                'report_region': 'PJM',
                'report_name': 'Day-Ahead LMP Report'
            },
            {
                'sensor_id': 'PJM_LMP_NORTH_HUB_RT',
                'description': 'PJM Northern Hub Real-Time Locational Marginal Price',
                'report_region': 'PJM',
                'report_name': 'Real-Time LMP Report'
            },
            {
                'sensor_id': 'CAISO_RT_LMP_SP15',
                'description': 'CAISO SP15 Real-Time Locational Marginal Price',
                'report_region': 'CAISO',
                'report_name': 'Real-Time LMP Report'
            },
            {
                'sensor_id': 'NYISO_DA_LMP_ZONE_A',
                'description': 'NYISO Zone A Day-Ahead Locational Marginal Price',
                'report_region': 'NYISO',
                'report_name': 'Day-Ahead LMP Report'
            },
            {
                'sensor_id': 'MISO_BAL_DAY_LMP_ILLINOIS',
                'description': 'MISO Illinois Balance Day Locational Marginal Price',
                'report_region': 'MISO',
                'report_name': 'Balance Day LMP Report'
            }
        ]

    def _create_mock_sensor_from_query(self, query_params: Dict[str, Any]) -> Dict[str, str]:
        """Create a mock sensor based on query parameters"""
        iso = query_params.get('iso', 'UNKNOWN').upper()
        data_type = query_params.get('data_type', 'lmp').upper()
        hub = query_params.get('hub_or_node', 'HUB').upper()

        return {
            'sensor_id': f'{iso}_{data_type}_{hub}_MOCK',
            'description': f'{iso} {hub} {data_type} Mock Sensor for Demonstration',
            'report_region': iso,
            'report_name': f'{data_type} Mock Report'
        }

    def _generate_mock_data(self, query_params: Dict[str, Any]) -> pd.DataFrame:
        """Generate realistic mock market data"""
        from datetime import datetime, timedelta

        # Determine date range
        start_date = query_params.get('start_date')
        end_date = query_params.get('end_date')

        if not start_date or not end_date:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
        else:
            start_date = pd.to_datetime(start_date)
            end_date = pd.to_datetime(end_date)

        # Generate hourly data
        dates = pd.date_range(start=start_date, end=end_date, freq='h')

        # Create realistic price patterns
        base_price = 50
        daily_pattern = 10 * np.sin(np.arange(len(dates)) * 2 * np.pi / 24)
        weekly_pattern = 5 * np.sin(np.arange(len(dates)) * 2 * np.pi / (24 * 7))
        noise = np.random.normal(0, 8, len(dates))

        # Add some spikes for realism
        spikes = np.random.choice([0, 1], size=len(dates), p=[0.95, 0.05])
        spike_values = spikes * np.random.uniform(20, 100, len(dates))

        prices = base_price + daily_pattern + weekly_pattern + noise + spike_values
        prices = np.maximum(prices, 5)  # Ensure no negative prices

        return pd.DataFrame({
            'timestamp': dates,
            'price': prices
        })

    def _perform_traditional_analysis(self, data: pd.DataFrame, query_params: Dict[str, Any]) -> Dict[str, Any]:
        """Perform traditional analysis on the data"""
        analysis_type = query_params.get('analysis_type', 'statistics')

        try:
            if analysis_type == 'rmse':
                # Calculate RMSE using moving average as baseline
                data['ma_24h'] = data['price'].rolling(window=24, min_periods=1).mean()
                rmse = np.sqrt(np.mean((data['price'] - data['ma_24h'])**2))
                mae = np.mean(np.abs(data['price'] - data['ma_24h']))

                return {
                    'rmse': rmse,
                    'mae': mae,
                    'mean_price': data['price'].mean(),
                    'data_points': len(data)
                }

            elif analysis_type == 'volatility':
                # Calculate volatility
                daily_returns = data['price'].pct_change().dropna()
                daily_vol = daily_returns.std() * 100
                annualized_vol = daily_vol * np.sqrt(365)

                return {
                    'daily_volatility': daily_vol,
                    'annualized_volatility': annualized_vol,
                    'mean_price': data['price'].mean(),
                    'data_points': len(data)
                }

            elif analysis_type == 'peak_analysis':
                # Peak vs off-peak analysis
                data['hour'] = data['timestamp'].dt.hour
                peak_hours = data[data['hour'].between(7, 22)]
                off_peak_hours = data[~data['hour'].between(7, 22)]

                peak_avg = peak_hours['price'].mean()
                off_peak_avg = off_peak_hours['price'].mean()
                premium = peak_avg - off_peak_avg

                return {
                    'peak_average': peak_avg,
                    'off_peak_average': off_peak_avg,
                    'peak_premium': premium,
                    'peak_premium_percentage': (premium / off_peak_avg) * 100,
                    'data_points': len(data)
                }

            else:  # statistics
                return {
                    'mean': data['price'].mean(),
                    'median': data['price'].median(),
                    'std_dev': data['price'].std(),
                    'min_price': data['price'].min(),
                    'max_price': data['price'].max(),
                    'data_points': len(data)
                }

        except Exception as e:
            logger.error(f"Error in traditional analysis: {str(e)}")
            return {'error': f'Analysis failed: {str(e)}'}
