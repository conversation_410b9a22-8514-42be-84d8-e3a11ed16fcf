#!/usr/bin/env python3
"""
Fast NLP service for quick query parsing without AI delays
"""

import re
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class FastMarketAnalyzer:
    """Fast rule-based market query analyzer"""
    
    def __init__(self):
        """Initialize the fast analyzer"""
        self.iso_patterns = {
            'ERCOT': ['ercot', 'texas'],
            'PJM': ['pjm', 'pennsylvania'],
            'CAISO': ['caiso', 'california', 'ca'],
            'NYISO': ['nyiso', 'new york', 'ny'],
            'MISO': ['miso', 'midwest'],
            'ISONE': ['isone', 'iso-ne', 'new england'],
            'SPP': ['spp', 'southwest']
        }
        
        self.data_type_patterns = {
            'nddam': ['nddam', 'next day', 'next-day'],
            'rt': ['rt', 'real-time', 'real time', 'realtime'],
            'da': ['da', 'day-ahead', 'day ahead', 'dayahead'],
            'bal_day': ['bal day', 'balance day', 'balancing'],
            'lmp': ['lmp', 'locational marginal', 'price'],
            'load': ['load', 'demand'],
            'generation': ['generation', 'gen', 'supply']
        }
        
        self.analysis_patterns = {
            'rmse': ['rmse', 'root mean square', 'rms error'],
            'mae': ['mae', 'mean absolute error'],
            'mape': ['mape', 'mean absolute percentage'],
            'volatility': ['volatility', 'vol', 'variance'],
            'trend': ['trend', 'trending', 'direction'],
            'peak_analysis': ['peak', 'off-peak', 'on-peak', 'peak vs'],
            'correlation': ['correlation', 'corr', 'relationship'],
            'statistics': ['statistics', 'stats', 'summary', 'basic']
        }
        
        self.hub_patterns = [
            'north', 'south', 'east', 'west', 'central',
            'hub', 'zone', 'node', 'sp15', 'np15', 'zp26'
        ]
    
    def parse_query(self, user_query: str) -> Dict[str, Any]:
        """Fast rule-based query parsing"""
        query_lower = user_query.lower()
        
        # Extract ISO
        iso = self._extract_iso(query_lower)
        
        # Extract data type
        data_type = self._extract_data_type(query_lower)
        
        # Extract hub/node
        hub_or_node = self._extract_hub(query_lower)
        
        # Extract analysis type
        analysis_type = self._extract_analysis_type(query_lower)
        
        # Extract time period
        time_period = self._extract_time_period(query_lower)
        
        # Calculate dates
        start_date, end_date = self._calculate_date_range(time_period)
        
        # Calculate confidence
        confidence = self._calculate_confidence(iso, data_type, analysis_type, time_period)
        
        return {
            'iso': iso,
            'data_type': data_type,
            'hub_or_node': hub_or_node,
            'analysis_type': analysis_type,
            'time_period': time_period,
            'start_date': start_date,
            'end_date': end_date,
            'confidence': confidence
        }
    
    def _extract_iso(self, query_lower: str) -> str:
        """Extract ISO from query"""
        for iso, patterns in self.iso_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                return iso
        return None
    
    def _extract_data_type(self, query_lower: str) -> str:
        """Extract data type from query"""
        for data_type, patterns in self.data_type_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                return data_type
        return 'lmp'  # default
    
    def _extract_hub(self, query_lower: str) -> str:
        """Extract hub/node from query"""
        for pattern in self.hub_patterns:
            if pattern in query_lower:
                return pattern
        return None
    
    def _extract_analysis_type(self, query_lower: str) -> str:
        """Extract analysis type from query"""
        for analysis, patterns in self.analysis_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                return analysis
        return 'statistics'  # default
    
    def _extract_time_period(self, query_lower: str) -> str:
        """Extract time period from query"""
        if any(term in query_lower for term in ['this week', 'week']):
            return 'this week'
        elif 'last week' in query_lower:
            return 'last week'
        elif any(term in query_lower for term in ['this month', 'month']):
            return 'this month'
        elif 'last month' in query_lower:
            return 'last month'
        elif 'today' in query_lower:
            return 'today'
        elif 'yesterday' in query_lower:
            return 'yesterday'
        return 'this week'  # default
    
    def _calculate_date_range(self, time_period: str) -> tuple:
        """Calculate start and end dates"""
        now = datetime.now()
        
        if time_period == 'today':
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = now
        elif time_period == 'yesterday':
            yesterday = now - timedelta(days=1)
            start_date = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = yesterday.replace(hour=23, minute=59, second=59)
        elif time_period == 'this week':
            days_since_monday = now.weekday()
            start_date = now - timedelta(days=days_since_monday)
            start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = now
        elif time_period == 'last week':
            days_since_monday = now.weekday()
            this_monday = now - timedelta(days=days_since_monday)
            start_date = this_monday - timedelta(days=7)
            end_date = this_monday - timedelta(seconds=1)
        elif time_period == 'this month':
            start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            end_date = now
        elif time_period == 'last month':
            first_this_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            end_date = first_this_month - timedelta(seconds=1)
            start_date = end_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        else:
            # Default to this week
            days_since_monday = now.weekday()
            start_date = now - timedelta(days=days_since_monday)
            start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = now
        
        return start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')
    
    def _calculate_confidence(self, iso: str, data_type: str, analysis_type: str, time_period: str) -> float:
        """Calculate confidence score"""
        confidence = 0.5  # base confidence
        
        if iso:
            confidence += 0.3
        if data_type and data_type != 'lmp':
            confidence += 0.2
        if analysis_type and analysis_type != 'statistics':
            confidence += 0.2
        if time_period:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def find_best_sensor(self, query_params: Dict[str, Any], available_sensors: List[Dict]) -> Dict[str, Any]:
        """Find best matching sensor"""
        iso = query_params.get('iso', '') or ''
        data_type = query_params.get('data_type', '') or ''
        hub = query_params.get('hub_or_node', '') or ''

        iso = iso.upper()
        data_type = data_type.lower()
        hub = hub.lower()

        best_sensor = None
        best_score = 0

        for sensor in available_sensors:
            score = 0
            sensor_id = sensor.get('sensor_id', '').upper()
            description = sensor.get('description', '').lower()

            # ISO match
            if iso and iso in sensor_id:
                score += 3

            # Data type match
            if data_type:
                if data_type in sensor_id.lower() or data_type in description:
                    score += 2

            # Hub match
            if hub and (hub in sensor_id.lower() or hub in description):
                score += 1

            if score > best_score:
                best_score = score
                best_sensor = sensor

        return best_sensor
    
    def generate_mock_data(self, query_params: Dict[str, Any]) -> pd.DataFrame:
        """Generate realistic mock market data"""
        start_date = pd.to_datetime(query_params.get('start_date'))
        end_date = pd.to_datetime(query_params.get('end_date'))
        
        # Generate hourly data
        dates = pd.date_range(start=start_date, end=end_date, freq='h')
        
        # Create realistic price patterns
        base_price = 50
        daily_pattern = 10 * np.sin(np.arange(len(dates)) * 2 * np.pi / 24)
        weekly_pattern = 5 * np.sin(np.arange(len(dates)) * 2 * np.pi / (24 * 7))
        noise = np.random.normal(0, 8, len(dates))
        
        # Add some spikes for realism
        spikes = np.random.choice([0, 1], size=len(dates), p=[0.95, 0.05])
        spike_values = spikes * np.random.uniform(20, 100, len(dates))
        
        prices = base_price + daily_pattern + weekly_pattern + noise + spike_values
        prices = np.maximum(prices, 5)  # Ensure no negative prices
        
        return pd.DataFrame({
            'timestamp': dates,
            'price': prices
        })
    
    def perform_analysis(self, data: pd.DataFrame, analysis_type: str) -> Dict[str, Any]:
        """Perform analysis on the data"""
        try:
            if analysis_type == 'rmse':
                # Calculate RMSE using moving average as baseline
                data['ma_24h'] = data['price'].rolling(window=24, min_periods=1).mean()
                rmse = np.sqrt(np.mean((data['price'] - data['ma_24h'])**2))
                mae = np.mean(np.abs(data['price'] - data['ma_24h']))
                
                return {
                    'rmse': rmse,
                    'mae': mae,
                    'mean_price': data['price'].mean(),
                    'data_points': len(data)
                }
                
            elif analysis_type == 'volatility':
                # Calculate volatility
                daily_returns = data['price'].pct_change().dropna()
                daily_vol = daily_returns.std() * 100
                annualized_vol = daily_vol * np.sqrt(365)
                
                return {
                    'daily_volatility': daily_vol,
                    'annualized_volatility': annualized_vol,
                    'mean_price': data['price'].mean(),
                    'data_points': len(data)
                }
                
            elif analysis_type == 'peak_analysis':
                # Peak vs off-peak analysis
                data['hour'] = data['timestamp'].dt.hour
                peak_hours = data[data['hour'].between(7, 22)]
                off_peak_hours = data[~data['hour'].between(7, 22)]
                
                peak_avg = peak_hours['price'].mean()
                off_peak_avg = off_peak_hours['price'].mean()
                premium = peak_avg - off_peak_avg
                
                return {
                    'peak_average': peak_avg,
                    'off_peak_average': off_peak_avg,
                    'peak_premium': premium,
                    'peak_premium_percentage': (premium / off_peak_avg) * 100,
                    'data_points': len(data)
                }
                
            else:  # statistics
                return {
                    'mean': data['price'].mean(),
                    'median': data['price'].median(),
                    'std_dev': data['price'].std(),
                    'min_price': data['price'].min(),
                    'max_price': data['price'].max(),
                    'data_points': len(data)
                }
                
        except Exception as e:
            logger.error(f"Error in analysis: {str(e)}")
            return {'error': f'Analysis failed: {str(e)}'}
    
    def get_mock_sensors(self) -> List[Dict]:
        """Get mock sensors for demonstration"""
        return [
            {
                'sensor_id': 'ERCOT_LMP_NORTH_HUB_RT',
                'description': 'ERCOT North Hub Real-Time Locational Marginal Price',
                'report_region': 'ERCOT',
                'report_name': 'Real-Time LMP Report'
            },
            {
                'sensor_id': 'ERCOT_NDDAM_SOUTH_HUB',
                'description': 'ERCOT South Hub Next Day Day-Ahead Market Price',
                'report_region': 'ERCOT',
                'report_name': 'Day-Ahead Market Report'
            },
            {
                'sensor_id': 'PJM_LMP_WEST_HUB_DA',
                'description': 'PJM Western Hub Day-Ahead Locational Marginal Price',
                'report_region': 'PJM',
                'report_name': 'Day-Ahead LMP Report'
            },
            {
                'sensor_id': 'PJM_LMP_NORTH_HUB_RT',
                'description': 'PJM Northern Hub Real-Time Locational Marginal Price',
                'report_region': 'PJM',
                'report_name': 'Real-Time LMP Report'
            },
            {
                'sensor_id': 'CAISO_RT_LMP_SP15',
                'description': 'CAISO SP15 Real-Time Locational Marginal Price',
                'report_region': 'CAISO',
                'report_name': 'Real-Time LMP Report'
            },
            {
                'sensor_id': 'NYISO_DA_LMP_ZONE_A',
                'description': 'NYISO Zone A Day-Ahead Locational Marginal Price',
                'report_region': 'NYISO',
                'report_name': 'Day-Ahead LMP Report'
            },
            {
                'sensor_id': 'MISO_BAL_DAY_LMP_ILLINOIS',
                'description': 'MISO Illinois Balance Day Locational Marginal Price',
                'report_region': 'MISO',
                'report_name': 'Balance Day LMP Report'
            }
        ]
