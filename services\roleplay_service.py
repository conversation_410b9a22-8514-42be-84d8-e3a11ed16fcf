"""
Roleplay Training Service - Core orchestration for AI-powered roleplay training sessions
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import json

logger = logging.getLogger(__name__)

class PersonaType(Enum):
    CLIENT = "client"
    VENDOR = "vendor"
    CONSULTANT = "consultant"

class MoodType(Enum):
    CURIOUS = "curious"
    SKEPTICAL = "skeptical"
    INDIFFERENT = "indifferent"
    PASSIVE = "passive"
    NOT_INTERESTED = "not_interested"

class SessionStatus(Enum):
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

@dataclass
class RoleplaySession:
    """Represents a roleplay training session"""
    session_id: str
    persona_type: PersonaType
    mood_type: MoodType
    agenda: str
    status: SessionStatus
    created_at: datetime
    updated_at: datetime
    conversation_history: List[Dict[str, Any]]
    feedback_history: List[Dict[str, Any]]
    current_question_count: int
    max_questions: int
    confidence_score: float
    strengths: List[str]
    improvement_areas: List[str]
    missed_points: List[str]

@dataclass
class PersonaProfile:
    """Defines characteristics of a roleplay persona"""
    persona_type: PersonaType
    name: str
    role: str
    company_type: str
    priorities: List[str]
    challenges: List[str]
    decision_factors: List[str]
    communication_style: str
    expertise_level: str
    typical_concerns: List[str]

@dataclass
class MoodProfile:
    """Defines behavior patterns for different moods"""
    mood_type: MoodType
    description: str
    question_style: str
    response_patterns: List[str]
    challenge_level: str
    engagement_level: str
    typical_reactions: List[str]

class RoleplayService:
    """Main service for managing roleplay training sessions"""
    
    def __init__(self, ai_analyzer=None):
        self.ai_analyzer = ai_analyzer
        self.active_sessions: Dict[str, RoleplaySession] = {}
        self.persona_profiles = self._initialize_persona_profiles()
        self.mood_profiles = self._initialize_mood_profiles()
        
    def _initialize_persona_profiles(self) -> Dict[PersonaType, PersonaProfile]:
        """Initialize predefined persona profiles"""
        return {
            PersonaType.CLIENT: PersonaProfile(
                persona_type=PersonaType.CLIENT,
                name="Energy Sector Client",
                role="VP of Strategy",
                company_type="Energy Company",
                priorities=["Cost reduction", "Risk management", "Regulatory compliance", "Market positioning"],
                challenges=["Energy transition", "Price volatility", "Supply chain risks", "ESG requirements"],
                decision_factors=["ROI", "Risk mitigation", "Timeline", "Regulatory impact"],
                communication_style="Direct, results-focused",
                expertise_level="High industry knowledge",
                typical_concerns=["Financial impact", "Implementation risks", "Competitive advantage"]
            ),
            PersonaType.VENDOR: PersonaProfile(
                persona_type=PersonaType.VENDOR,
                name="Technology Vendor",
                role="Sales Director",
                company_type="Technology Provider",
                priorities=["Solution fit", "Partnership value", "Implementation success", "Long-term relationship"],
                challenges=["Competitive landscape", "Technical integration", "Customer adoption", "Pricing pressure"],
                decision_factors=["Technical capabilities", "Support quality", "Cost-effectiveness", "Scalability"],
                communication_style="Solution-oriented, collaborative",
                expertise_level="Deep technical knowledge",
                typical_concerns=["Technical feasibility", "Integration complexity", "Support requirements"]
            ),
            PersonaType.CONSULTANT: PersonaProfile(
                persona_type=PersonaType.CONSULTANT,
                name="Strategy Consultant",
                role="Principal Consultant",
                company_type="Consulting Firm",
                priorities=["Strategic alignment", "Best practices", "Risk assessment", "Value optimization"],
                challenges=["Complex stakeholder needs", "Changing market dynamics", "Implementation barriers"],
                decision_factors=["Strategic fit", "Implementation feasibility", "Change management", "Measurable outcomes"],
                communication_style="Analytical, strategic",
                expertise_level="Cross-industry expertise",
                typical_concerns=["Strategic implications", "Change management", "Stakeholder alignment"]
            )
        }
    
    def _initialize_mood_profiles(self) -> Dict[MoodType, MoodProfile]:
        """Initialize predefined mood profiles"""
        return {
            MoodType.CURIOUS: MoodProfile(
                mood_type=MoodType.CURIOUS,
                description="Engaged and inquisitive, seeking detailed understanding",
                question_style="Exploratory, detailed follow-ups",
                response_patterns=["Tell me more about...", "How does this work with...", "What if we consider..."],
                challenge_level="Medium",
                engagement_level="High",
                typical_reactions=["Asks for examples", "Explores alternatives", "Seeks deeper understanding"]
            ),
            MoodType.SKEPTICAL: MoodProfile(
                mood_type=MoodType.SKEPTICAL,
                description="Doubtful and challenging, needs convincing evidence",
                question_style="Probing, challenging assumptions",
                response_patterns=["How can you prove...", "What about the risks of...", "I'm not convinced that..."],
                challenge_level="High",
                engagement_level="High",
                typical_reactions=["Questions assumptions", "Highlights risks", "Demands evidence"]
            ),
            MoodType.INDIFFERENT: MoodProfile(
                mood_type=MoodType.INDIFFERENT,
                description="Neutral stance, neither positive nor negative",
                question_style="Factual, straightforward",
                response_patterns=["What are the facts about...", "Show me the numbers", "What's the bottom line"],
                challenge_level="Medium",
                engagement_level="Medium",
                typical_reactions=["Focuses on facts", "Asks for data", "Seeks practical information"]
            ),
            MoodType.PASSIVE: MoodProfile(
                mood_type=MoodType.PASSIVE,
                description="Low engagement, minimal participation",
                question_style="Brief, minimal follow-up",
                response_patterns=["Okay", "I see", "What else?"],
                challenge_level="Low",
                engagement_level="Low",
                typical_reactions=["Short responses", "Minimal questions", "Waits for information"]
            ),
            MoodType.NOT_INTERESTED: MoodProfile(
                mood_type=MoodType.NOT_INTERESTED,
                description="Disengaged, looking for reasons to end discussion",
                question_style="Dismissive, time-focused",
                response_patterns=["How long will this take?", "Is this really necessary?", "Can we wrap this up?"],
                challenge_level="High",
                engagement_level="Very Low",
                typical_reactions=["Shows impatience", "Questions value", "Seeks to end meeting"]
            )
        }
    
    def create_session(self, persona_type: str, mood_type: str, agenda: str, max_questions: int = 5) -> Dict[str, Any]:
        """Create a new roleplay training session"""
        try:
            session_id = str(uuid.uuid4())
            
            # Validate inputs
            persona_enum = PersonaType(persona_type.lower())
            mood_enum = MoodType(mood_type.lower())
            
            # Create session
            session = RoleplaySession(
                session_id=session_id,
                persona_type=persona_enum,
                mood_type=mood_enum,
                agenda=agenda,
                status=SessionStatus.ACTIVE,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                conversation_history=[],
                feedback_history=[],
                current_question_count=0,
                max_questions=max_questions,
                confidence_score=0.0,
                strengths=[],
                improvement_areas=[],
                missed_points=[]
            )
            
            self.active_sessions[session_id] = session
            
            # Generate initial context and scenario
            context = self._generate_session_context(session)
            
            return {
                'session_id': session_id,
                'persona_profile': self._serialize_persona_profile(self.persona_profiles[persona_enum]),
                'mood_profile': self._serialize_mood_profile(self.mood_profiles[mood_enum]),
                'context': context,
                'status': 'created'
            }
            
        except ValueError as e:
            logger.error(f"Invalid persona or mood type: {e}")
            return {'error': f'Invalid persona or mood type: {e}'}
        except Exception as e:
            logger.error(f"Error creating roleplay session: {e}")
            return {'error': f'Failed to create session: {e}'}

    def _serialize_persona_profile(self, persona_profile: PersonaProfile) -> Dict[str, Any]:
        """Serialize persona profile for JSON response"""
        return {
            'persona_type': persona_profile.persona_type.value,
            'name': persona_profile.name,
            'role': persona_profile.role,
            'company_type': persona_profile.company_type,
            'priorities': persona_profile.priorities,
            'challenges': persona_profile.challenges,
            'decision_factors': persona_profile.decision_factors,
            'communication_style': persona_profile.communication_style,
            'expertise_level': persona_profile.expertise_level,
            'typical_concerns': persona_profile.typical_concerns
        }

    def _serialize_mood_profile(self, mood_profile: MoodProfile) -> Dict[str, Any]:
        """Serialize mood profile for JSON response"""
        return {
            'mood_type': mood_profile.mood_type.value,
            'description': mood_profile.description,
            'question_style': mood_profile.question_style,
            'response_patterns': mood_profile.response_patterns,
            'challenge_level': mood_profile.challenge_level,
            'engagement_level': mood_profile.engagement_level,
            'typical_reactions': mood_profile.typical_reactions
        }

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data by session ID"""
        if session_id not in self.active_sessions:
            return None

        session = self.active_sessions[session_id]
        return {
            'session_id': session_id,
            'persona_type': session.persona_type.value,
            'mood_type': session.mood_type.value,
            'agenda': session.agenda,
            'status': session.status.value,
            'current_question_count': session.current_question_count,
            'max_questions': session.max_questions,
            'conversation_history': session.conversation_history,
            'feedback_history': session.feedback_history,
            'created_at': session.created_at.isoformat(),
            'updated_at': session.updated_at.isoformat()
        }
    
    def _generate_session_context(self, session: RoleplaySession) -> Dict[str, Any]:
        """Generate contextual background for the roleplay session"""
        persona_profile = self.persona_profiles[session.persona_type]
        mood_profile = self.mood_profiles[session.mood_type]
        
        # Create realistic context based on persona and agenda
        context = {
            'scenario_description': f"You are meeting with a {persona_profile.role} from a {persona_profile.company_type}. "
                                  f"They are {mood_profile.description.lower()}. "
                                  f"The meeting agenda is: {session.agenda}",
            'persona_background': {
                'role': persona_profile.role,
                'company_type': persona_profile.company_type,
                'priorities': persona_profile.priorities,
                'challenges': persona_profile.challenges,
                'communication_style': persona_profile.communication_style
            },
            'mood_indicators': {
                'description': mood_profile.description,
                'engagement_level': mood_profile.engagement_level,
                'challenge_level': mood_profile.challenge_level,
                'typical_reactions': mood_profile.typical_reactions
            },
            'session_info': {
                'max_questions': session.max_questions,
                'current_question': session.current_question_count,
                'agenda': session.agenda
            }
        }
        
        return context
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve session information"""
        session = self.active_sessions.get(session_id)
        if not session:
            return None
            
        return {
            'session': asdict(session),
            'persona_profile': asdict(self.persona_profiles[session.persona_type]),
            'mood_profile': asdict(self.mood_profiles[session.mood_type])
        }
    
    def end_session(self, session_id: str) -> Dict[str, Any]:
        """End a roleplay session and generate final evaluation"""
        session = self.active_sessions.get(session_id)
        if not session:
            return {'error': 'Session not found'}
        
        session.status = SessionStatus.COMPLETED
        session.updated_at = datetime.now()
        
        # Generate final evaluation
        evaluation = self._generate_final_evaluation(session)
        
        return {
            'session_id': session_id,
            'status': 'completed',
            'evaluation': evaluation
        }
    
    def _generate_final_evaluation(self, session: RoleplaySession) -> Dict[str, Any]:
        """Generate comprehensive final evaluation"""
        # Calculate overall performance metrics
        total_interactions = len(session.conversation_history)
        feedback_scores = [f.get('score', 0) for f in session.feedback_history if 'score' in f]
        avg_score = sum(feedback_scores) / len(feedback_scores) if feedback_scores else 0
        
        evaluation = {
            'overall_score': round(avg_score, 2),
            'confidence_level': self._calculate_confidence_level(avg_score),
            'total_interactions': total_interactions,
            'questions_handled': session.current_question_count,
            'strengths': session.strengths,
            'improvement_areas': session.improvement_areas,
            'missed_points': session.missed_points,
            'recommendations': self._generate_recommendations(session, avg_score),
            'session_summary': {
                'persona': session.persona_type.value,
                'mood': session.mood_type.value,
                'agenda': session.agenda,
                'duration_minutes': (session.updated_at - session.created_at).total_seconds() / 60
            }
        }
        
        return evaluation
    
    def _calculate_confidence_level(self, score: float) -> str:
        """Calculate confidence level based on score"""
        if score >= 8.5:
            return "Excellent"
        elif score >= 7.0:
            return "Good"
        elif score >= 5.5:
            return "Satisfactory"
        elif score >= 4.0:
            return "Needs Improvement"
        else:
            return "Requires Significant Work"
    
    def _generate_recommendations(self, session: RoleplaySession, avg_score: float) -> List[str]:
        """Generate personalized recommendations based on session performance"""
        recommendations = []
        
        persona_profile = self.persona_profiles[session.persona_type]
        mood_profile = self.mood_profiles[session.mood_type]
        
        # Score-based recommendations
        if avg_score < 6.0:
            recommendations.append("Focus on understanding the persona's core priorities and concerns")
            recommendations.append("Practice active listening and ask clarifying questions")
        
        # Persona-specific recommendations
        if session.persona_type == PersonaType.CLIENT:
            recommendations.append("Emphasize ROI and business value in your responses")
            recommendations.append("Address risk mitigation strategies proactively")
        elif session.persona_type == PersonaType.VENDOR:
            recommendations.append("Focus on technical capabilities and integration aspects")
            recommendations.append("Highlight partnership value and long-term support")
        elif session.persona_type == PersonaType.CONSULTANT:
            recommendations.append("Provide strategic context and industry best practices")
            recommendations.append("Address change management and implementation considerations")
        
        # Mood-specific recommendations
        if session.mood_type == MoodType.SKEPTICAL:
            recommendations.append("Prepare more evidence and case studies to support your points")
            recommendations.append("Anticipate objections and have ready responses")
        elif session.mood_type == MoodType.NOT_INTERESTED:
            recommendations.append("Lead with compelling value propositions")
            recommendations.append("Keep responses concise and impactful")
        
        return recommendations
