#!/usr/bin/env python3
"""
Test Genscape API directly to understand the correct format
"""

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

def test_genscape_api_direct():
    """Test Genscape API with different endpoint formats"""
    
    api_key = os.getenv('GENSCAPE_API_KEY')
    print(f"Testing Genscape API with key: {api_key[:10]}...")
    
    # Test different base URLs and endpoints
    test_configs = [
        {
            "name": "Original Config",
            "base_url": "https://api.genscape.com/marketintelligence/na",
            "metadata_endpoint": "/v1/searchreportmetadata",
            "data_endpoint": "/v1/getepcalcsiddata"
        },
        {
            "name": "Alternative Config 1", 
            "base_url": "https://api.genscape.com/marketintelligence",
            "metadata_endpoint": "/na/v1/searchreportmetadata",
            "data_endpoint": "/na/v1/getepcalcsiddata"
        },
        {
            "name": "Alternative Config 2",
            "base_url": "https://api.genscape.com",
            "metadata_endpoint": "/marketintelligence/na/v1/searchreportmetadata", 
            "data_endpoint": "/marketintelligence/na/v1/getepcalcsiddata"
        }
    ]
    
    # Test different authentication methods
    auth_methods = [
        {
            "name": "Bearer Token",
            "headers": {
                "Authorization": f"Bearer {api_key}",
                "Accept": "application/json",
                "Content-Type": "application/json"
            }
        },
        {
            "name": "X-API-Key Header",
            "headers": {
                "X-API-Key": api_key,
                "Accept": "application/json",
                "Content-Type": "application/json"
            }
        },
        {
            "name": "API Key Header",
            "headers": {
                "apikey": api_key,
                "Accept": "application/json",
                "Content-Type": "application/json"
            }
        },
        {
            "name": "Query Parameter",
            "headers": {
                "Accept": "application/json",
                "Content-Type": "application/json"
            },
            "params": {"apikey": api_key}
        }
    ]
    
    print("\n" + "="*60)
    print("TESTING METADATA API ENDPOINTS")
    print("="*60)
    
    working_config = None
    
    for config in test_configs:
        print(f"\n🔍 Testing {config['name']}")
        print(f"URL: {config['base_url']}{config['metadata_endpoint']}")
        
        for auth in auth_methods:
            print(f"\n   Auth Method: {auth['name']}")
            
            try:
                url = f"{config['base_url']}{config['metadata_endpoint']}"
                headers = auth['headers']
                params = auth.get('params', {})
                params.update({"limit": 5})  # Add limit parameter
                
                response = requests.get(url, headers=headers, params=params, timeout=10)
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    print("   ✅ SUCCESS!")
                    data = response.json()
                    print(f"   Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                    
                    if isinstance(data, dict) and 'reports' in data:
                        reports = data['reports']
                        print(f"   Found {len(reports)} reports")
                        if reports:
                            first_report = reports[0]
                            print(f"   First report: {first_report.get('reportName', 'No name')}")
                            if 'sids' in first_report:
                                print(f"   SIDs in first report: {len(first_report['sids'])}")
                    
                    working_config = {
                        "base_url": config['base_url'],
                        "metadata_endpoint": config['metadata_endpoint'],
                        "data_endpoint": config['data_endpoint'],
                        "auth": auth
                    }
                    
                    return working_config
                    
                elif response.status_code == 401:
                    print("   ❌ Unauthorized")
                elif response.status_code == 404:
                    print("   ❌ Not Found")
                else:
                    print(f"   ❌ Error: {response.text[:100]}")
                    
            except requests.exceptions.Timeout:
                print("   ⏰ Timeout")
            except Exception as e:
                print(f"   ❌ Exception: {str(e)}")
    
    return None

def test_data_api(working_config):
    """Test the data API with working configuration"""
    
    if not working_config:
        print("❌ No working configuration found for metadata API")
        return
    
    print("\n" + "="*60)
    print("TESTING DATA API ENDPOINT")
    print("="*60)
    
    # Test with some common parameters
    test_params = {
        "sids": "12345",  # Dummy SID for testing
        "start_date": "2025-09-15",
        "end_date": "2025-09-16", 
        "interval": "hourly"
    }
    
    url = f"{working_config['base_url']}{working_config['data_endpoint']}"
    headers = working_config['auth']['headers']
    params = working_config['auth'].get('params', {})
    params.update(test_params)
    
    print(f"Testing URL: {url}")
    print(f"Parameters: {test_params}")
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Data API endpoint is accessible!")
            data = response.json()
            print(f"Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
        elif response.status_code == 400:
            print("⚠️ Bad Request (expected with dummy SID)")
            print(f"Response: {response.text[:200]}")
        elif response.status_code == 404:
            print("❌ Data endpoint not found")
        else:
            print(f"❌ Error {response.status_code}: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def main():
    """Test Genscape API to find working configuration"""
    
    print("🔍 GENSCAPE API CONFIGURATION TESTER")
    print("This will help us find the correct API format for your key")
    
    # Test metadata API
    working_config = test_genscape_api_direct()
    
    if working_config:
        print(f"\n🎉 FOUND WORKING CONFIGURATION!")
        print(f"Base URL: {working_config['base_url']}")
        print(f"Metadata Endpoint: {working_config['metadata_endpoint']}")
        print(f"Auth Method: {working_config['auth']['name']}")
        
        # Test data API
        test_data_api(working_config)
        
        print(f"\n📝 UPDATE YOUR .env FILE:")
        print(f"MARKET_PRICE_API_URL={working_config['base_url']}")
        print(f"METADATA_API_URL={working_config['base_url']}")
        
        print(f"\n🔧 UPDATE API SERVICE:")
        print(f"Use auth method: {working_config['auth']['name']}")
        
    else:
        print(f"\n❌ NO WORKING CONFIGURATION FOUND")
        print(f"Please check:")
        print(f"1. API key is correct: {os.getenv('GENSCAPE_API_KEY', 'NOT FOUND')[:10]}...")
        print(f"2. API endpoints have changed")
        print(f"3. Additional authentication required")
        
        print(f"\n💡 NEXT STEPS:")
        print(f"1. Check Genscape API documentation")
        print(f"2. Verify the exact endpoints you use when it works")
        print(f"3. Check if there are additional headers needed")

if __name__ == "__main__":
    main()
