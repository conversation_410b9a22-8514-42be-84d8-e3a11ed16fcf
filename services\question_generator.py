"""
AI-Powered Question Generator - Intelligent question generation for roleplay training
"""

import logging
import json
import random
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from services.roleplay_service import PersonaType, MoodType

logger = logging.getLogger(__name__)

@dataclass
class QuestionContext:
    """Context for generating targeted questions"""
    persona_type: PersonaType
    mood_type: MoodType
    agenda: str
    industry: str
    conversation_history: List[Dict[str, Any]]
    current_question_number: int
    total_questions: int
    user_performance_score: float

@dataclass
class GeneratedQuestion:
    """A generated question with metadata"""
    question_text: str
    question_type: str
    difficulty_level: str
    expected_topics: List[str]
    evaluation_criteria: List[str]
    follow_up_hints: List[str]
    persona_context: str

class QuestionGenerator:
    """AI-powered question generator for roleplay training"""
    
    def __init__(self, ai_analyzer=None):
        self.ai_analyzer = ai_analyzer
        self.question_templates = self._initialize_question_templates()
        self.difficulty_progression = self._initialize_difficulty_progression()
        self.evaluation_frameworks = self._initialize_evaluation_frameworks()
        
    def _initialize_question_templates(self) -> Dict[str, Dict[str, List[str]]]:
        """Initialize question templates by persona and mood"""
        return {
            'client_curious': [
                "I'm interested in understanding how {topic} would integrate with our existing {context}. Can you walk me through the implementation process?",
                "What are the long-term implications of {topic} for our {industry} operations, particularly regarding {challenge}?",
                "How does your approach to {topic} compare to other solutions we might consider, especially in terms of {criteria}?",
                "Can you provide some real-world examples of how {topic} has helped similar organizations address {challenge}?",
                "What kind of support and training would our team need to successfully implement {topic}?"
            ],
            'client_skeptical': [
                "I've heard similar promises about {topic} before. How can you prove this won't be another failed initiative?",
                "What happens when {topic} doesn't deliver the expected {benefit}? What's your contingency plan?",
                "How do you address the significant risks associated with {topic}, particularly {risk_factor}?",
                "Your projections for {topic} seem overly optimistic. What evidence supports these claims?",
                "What guarantees can you provide that {topic} will actually solve our {challenge} problem?"
            ],
            'client_indifferent': [
                "What's the bottom line cost for {topic} and when will we see ROI?",
                "How long does {topic} implementation take and what resources do we need to commit?",
                "What are the key facts I need to know about {topic} for our decision?",
                "Is {topic} really necessary for our {objective}, or are there simpler alternatives?",
                "What's the minimum viable approach to {topic} that would meet our basic requirements?"
            ],
            'vendor_curious': [
                "I'd like to understand your specific requirements for {topic} better. What are your key success criteria?",
                "How does {topic} fit into your broader technology strategy and roadmap?",
                "What challenges have you faced with {topic} in the past, and how can we avoid those pitfalls?",
                "Can you help me understand the decision-making process for {topic} within your organization?",
                "What would make {topic} a strategic win for your team and the broader organization?"
            ],
            'vendor_skeptical': [
                "Are you really committed to {topic}, or is this just another evaluation exercise?",
                "What's your actual budget for {topic}, and do you have the authority to make this decision?",
                "How do I know you won't just take our proposal and use it to negotiate with competitors?",
                "What assurance do I have that you'll actually move forward with {topic} after this evaluation?",
                "Why should we invest our resources in {topic} when you might not be a serious buyer?"
            ],
            'consultant_curious': [
                "What are the strategic implications of {topic} for your organization's competitive positioning?",
                "How does {topic} align with industry best practices and emerging trends in {industry}?",
                "What change management considerations should we address for successful {topic} implementation?",
                "How do we measure the success of {topic} and ensure it delivers the expected value?",
                "What are the potential unintended consequences of {topic} that we should plan for?"
            ],
            'consultant_skeptical': [
                "Have you considered the full scope of organizational change required for {topic}?",
                "What makes you think {topic} will succeed when similar initiatives have failed?",
                "Are you prepared for the significant investment and commitment that {topic} requires?",
                "How will you handle the inevitable resistance to {topic} from various stakeholders?",
                "What's your plan B if {topic} doesn't deliver the expected results?"
            ]
        }
    
    def _initialize_difficulty_progression(self) -> Dict[int, str]:
        """Initialize difficulty progression for questions throughout the session"""
        return {
            1: 'warm_up',      # Easy, relationship-building questions
            2: 'exploratory',  # Medium difficulty, understanding needs
            3: 'challenging',  # Higher difficulty, testing knowledge
            4: 'complex',      # Complex scenarios and edge cases
            5: 'strategic'     # Strategic thinking and long-term implications
        }
    
    def _initialize_evaluation_frameworks(self) -> Dict[str, List[str]]:
        """Initialize evaluation criteria for different question types"""
        return {
            'technical_knowledge': [
                'Demonstrates understanding of technical concepts',
                'Provides accurate technical information',
                'Explains complex topics clearly',
                'Addresses technical concerns appropriately'
            ],
            'business_acumen': [
                'Shows understanding of business impact',
                'Connects solution to business value',
                'Addresses ROI and cost considerations',
                'Demonstrates market awareness'
            ],
            'communication_skills': [
                'Communicates clearly and concisely',
                'Adapts communication style to audience',
                'Uses appropriate examples and analogies',
                'Maintains professional demeanor'
            ],
            'problem_solving': [
                'Identifies key issues and challenges',
                'Proposes practical solutions',
                'Considers multiple perspectives',
                'Addresses potential obstacles'
            ],
            'relationship_building': [
                'Builds rapport and trust',
                'Shows empathy and understanding',
                'Asks thoughtful questions',
                'Demonstrates active listening'
            ]
        }
    
    def generate_question(self, context: QuestionContext) -> GeneratedQuestion:
        """Generate an AI-powered question based on context"""
        try:
            if self.ai_analyzer and self.ai_analyzer.model:
                return self._generate_ai_question(context)
            else:
                return self._generate_template_question(context)
                
        except Exception as e:
            logger.error(f"Error generating question: {e}")
            return self._generate_fallback_question(context)
    
    def _generate_ai_question(self, context: QuestionContext) -> GeneratedQuestion:
        """Generate question using AI"""
        try:
            # Prepare context for AI
            persona_mood_key = f"{context.persona_type.value}_{context.mood_type.value}"
            difficulty = self.difficulty_progression.get(context.current_question_number, 'medium')
            
            # Build conversation history summary
            history_summary = ""
            if context.conversation_history:
                recent_topics = [item.get('topic', '') for item in context.conversation_history[-3:]]
                history_summary = f"Recent discussion topics: {', '.join(recent_topics)}"
            
            prompt = f"""
You are an expert roleplay trainer creating realistic questions for business meeting practice.

Context:
- Persona: {context.persona_type.value.title()} ({self._get_persona_description(context.persona_type)})
- Mood: {context.mood_type.value.title()} ({self._get_mood_description(context.mood_type)})
- Meeting Agenda: {context.agenda}
- Industry: {context.industry}
- Question #{context.current_question_number} of {context.total_questions}
- Difficulty Level: {difficulty}
- User Performance So Far: {context.user_performance_score:.1f}/10
{history_summary}

Generate a realistic question that this persona would ask in this mood about the agenda topic.

Requirements:
1. The question should reflect the persona's role, priorities, and concerns
2. The mood should influence the tone and style of questioning
3. Difficulty should progress appropriately ({difficulty} level)
4. Avoid repeating topics from conversation history
5. Include specific business context relevant to {context.industry}

Return a JSON object with:
{{
    "question_text": "The actual question to ask",
    "question_type": "technical|business|strategic|relationship",
    "difficulty_level": "{difficulty}",
    "expected_topics": ["topic1", "topic2", "topic3"],
    "evaluation_criteria": ["criteria1", "criteria2", "criteria3"],
    "follow_up_hints": ["hint1", "hint2"],
    "persona_context": "Brief explanation of why this persona would ask this question"
}}

Return only valid JSON, no other text.
"""

            response = self.ai_analyzer.model.generate_content(prompt)
            
            # Parse AI response
            response_text = response.text.strip()
            if response_text.startswith('```json'):
                response_text = response_text.split('```json')[1].split('```')[0].strip()
            elif response_text.startswith('```'):
                response_text = response_text.split('```')[1].split('```')[0].strip()
            
            question_data = json.loads(response_text)
            
            return GeneratedQuestion(
                question_text=question_data['question_text'],
                question_type=question_data['question_type'],
                difficulty_level=question_data['difficulty_level'],
                expected_topics=question_data['expected_topics'],
                evaluation_criteria=question_data['evaluation_criteria'],
                follow_up_hints=question_data['follow_up_hints'],
                persona_context=question_data['persona_context']
            )
            
        except Exception as e:
            logger.error(f"AI question generation failed: {e}")
            return self._generate_template_question(context)
    
    def _generate_template_question(self, context: QuestionContext) -> GeneratedQuestion:
        """Generate question using templates as fallback"""
        try:
            persona_mood_key = f"{context.persona_type.value}_{context.mood_type.value}"
            templates = self.question_templates.get(persona_mood_key, [])
            
            if not templates:
                # Fallback to generic templates
                templates = self.question_templates.get('client_curious', [])
            
            # Select template based on difficulty progression
            difficulty = self.difficulty_progression.get(context.current_question_number, 'medium')
            template = random.choice(templates)
            
            # Fill in template variables
            question_text = self._fill_template_variables(template, context)
            
            # Determine question type and evaluation criteria
            question_type = self._determine_question_type(question_text, context)
            evaluation_criteria = self.evaluation_frameworks.get(question_type, [])
            
            return GeneratedQuestion(
                question_text=question_text,
                question_type=question_type,
                difficulty_level=difficulty,
                expected_topics=self._extract_expected_topics(context),
                evaluation_criteria=evaluation_criteria[:3],  # Limit to 3 criteria
                follow_up_hints=self._generate_follow_up_hints(context),
                persona_context=self._generate_persona_context(context)
            )
            
        except Exception as e:
            logger.error(f"Template question generation failed: {e}")
            return self._generate_fallback_question(context)
    
    def _fill_template_variables(self, template: str, context: QuestionContext) -> str:
        """Fill template variables with context-appropriate values"""
        variables = {
            'topic': context.agenda,
            'context': self._get_context_term(context),
            'industry': context.industry,
            'challenge': self._get_relevant_challenge(context),
            'criteria': self._get_decision_criteria(context),
            'benefit': self._get_expected_benefit(context),
            'risk_factor': self._get_risk_factor(context),
            'objective': self._get_business_objective(context)
        }
        
        # Replace variables in template
        filled_template = template
        for var, value in variables.items():
            filled_template = filled_template.replace(f'{{{var}}}', value)
        
        return filled_template
    
    def _get_context_term(self, context: QuestionContext) -> str:
        """Get appropriate context term based on persona"""
        context_terms = {
            PersonaType.CLIENT: ['infrastructure', 'operations', 'systems', 'processes'],
            PersonaType.VENDOR: ['platform', 'solution', 'technology stack', 'architecture'],
            PersonaType.CONSULTANT: ['framework', 'methodology', 'approach', 'strategy']
        }
        return random.choice(context_terms.get(context.persona_type, ['systems']))
    
    def _get_relevant_challenge(self, context: QuestionContext) -> str:
        """Get relevant challenge based on industry and persona"""
        challenges = {
            'energy': ['grid reliability', 'regulatory compliance', 'cost optimization', 'sustainability goals'],
            'technology': ['scalability', 'security', 'integration complexity', 'user adoption'],
            'financial': ['risk management', 'regulatory compliance', 'operational efficiency', 'customer experience']
        }
        return random.choice(challenges.get(context.industry, ['operational efficiency']))
    
    def _get_decision_criteria(self, context: QuestionContext) -> str:
        """Get decision criteria based on persona type"""
        criteria = {
            PersonaType.CLIENT: ['ROI', 'risk mitigation', 'strategic alignment', 'implementation timeline'],
            PersonaType.VENDOR: ['technical fit', 'partnership value', 'scalability', 'support quality'],
            PersonaType.CONSULTANT: ['best practices', 'change management', 'stakeholder impact', 'measurable outcomes']
        }
        return random.choice(criteria.get(context.persona_type, ['value proposition']))
    
    def _get_expected_benefit(self, context: QuestionContext) -> str:
        """Get expected benefit based on context"""
        benefits = ['cost savings', 'efficiency gains', 'risk reduction', 'competitive advantage', 'revenue growth']
        return random.choice(benefits)
    
    def _get_risk_factor(self, context: QuestionContext) -> str:
        """Get risk factor based on industry"""
        risks = {
            'energy': ['regulatory changes', 'market volatility', 'infrastructure failures', 'cybersecurity threats'],
            'technology': ['data breaches', 'system downtime', 'integration failures', 'vendor lock-in'],
            'financial': ['market risk', 'operational risk', 'compliance violations', 'liquidity issues']
        }
        return random.choice(risks.get(context.industry, ['operational disruption']))
    
    def _get_business_objective(self, context: QuestionContext) -> str:
        """Get business objective based on persona"""
        objectives = {
            PersonaType.CLIENT: ['growth strategy', 'operational excellence', 'market leadership', 'innovation'],
            PersonaType.VENDOR: ['customer success', 'market expansion', 'solution optimization', 'partnership growth'],
            PersonaType.CONSULTANT: ['strategic transformation', 'performance improvement', 'organizational change', 'value creation']
        }
        return random.choice(objectives.get(context.persona_type, ['business success']))
    
    def _determine_question_type(self, question_text: str, context: QuestionContext) -> str:
        """Determine question type based on content and context"""
        question_lower = question_text.lower()
        
        if any(word in question_lower for word in ['technical', 'integration', 'system', 'technology']):
            return 'technical_knowledge'
        elif any(word in question_lower for word in ['roi', 'cost', 'business', 'value', 'revenue']):
            return 'business_acumen'
        elif any(word in question_lower for word in ['strategy', 'long-term', 'competitive', 'market']):
            return 'strategic'
        elif any(word in question_lower for word in ['team', 'support', 'relationship', 'partnership']):
            return 'relationship_building'
        else:
            return 'problem_solving'
    
    def _extract_expected_topics(self, context: QuestionContext) -> List[str]:
        """Extract expected topics from agenda and context"""
        agenda_words = context.agenda.lower().split()
        topics = []
        
        # Add agenda-related topics
        for word in agenda_words:
            if len(word) > 3:  # Skip short words
                topics.append(word.title())
        
        # Add context-specific topics
        if context.industry == 'energy':
            topics.extend(['Market Analysis', 'Regulatory Impact', 'Grid Operations'])
        elif context.industry == 'technology':
            topics.extend(['Technical Architecture', 'Implementation Plan', 'User Experience'])
        elif context.industry == 'financial':
            topics.extend(['Risk Assessment', 'Compliance Requirements', 'Financial Impact'])
        
        return topics[:3]  # Limit to 3 topics
    
    def _generate_follow_up_hints(self, context: QuestionContext) -> List[str]:
        """Generate follow-up hints based on context"""
        hints = []
        
        if context.mood_type == MoodType.SKEPTICAL:
            hints.extend(['Provide concrete evidence', 'Address specific concerns', 'Use third-party validation'])
        elif context.mood_type == MoodType.CURIOUS:
            hints.extend(['Offer detailed examples', 'Suggest additional resources', 'Encourage questions'])
        elif context.mood_type == MoodType.NOT_INTERESTED:
            hints.extend(['Focus on immediate value', 'Keep response concise', 'Highlight quick wins'])
        
        return hints[:2]  # Limit to 2 hints
    
    def _generate_persona_context(self, context: QuestionContext) -> str:
        """Generate persona context explanation"""
        persona_contexts = {
            PersonaType.CLIENT: f"As a client in the {context.industry} industry, this persona is focused on business value and risk mitigation.",
            PersonaType.VENDOR: f"As a vendor, this persona is interested in understanding customer needs and demonstrating solution value.",
            PersonaType.CONSULTANT: f"As a consultant, this persona is evaluating strategic fit and implementation considerations."
        }
        return persona_contexts.get(context.persona_type, "This persona is evaluating the proposal from their professional perspective.")
    
    def _get_persona_description(self, persona_type: PersonaType) -> str:
        """Get brief persona description"""
        descriptions = {
            PersonaType.CLIENT: "Decision-maker focused on business value and risk",
            PersonaType.VENDOR: "Solution provider seeking customer fit and partnership",
            PersonaType.CONSULTANT: "Strategic advisor evaluating best practices and implementation"
        }
        return descriptions.get(persona_type, "Business professional")
    
    def _get_mood_description(self, mood_type: MoodType) -> str:
        """Get brief mood description"""
        descriptions = {
            MoodType.CURIOUS: "Engaged and seeking detailed understanding",
            MoodType.SKEPTICAL: "Doubtful and requiring convincing evidence",
            MoodType.INDIFFERENT: "Neutral and focused on facts",
            MoodType.PASSIVE: "Low engagement, minimal participation",
            MoodType.NOT_INTERESTED: "Disengaged and time-conscious"
        }
        return descriptions.get(mood_type, "Professional demeanor")
    
    def _generate_fallback_question(self, context: QuestionContext) -> GeneratedQuestion:
        """Generate a basic fallback question when all else fails"""
        return GeneratedQuestion(
            question_text=f"Can you tell me more about how {context.agenda} would work in our {context.industry} context?",
            question_type='general',
            difficulty_level='medium',
            expected_topics=[context.agenda, context.industry, 'Implementation'],
            evaluation_criteria=['Clear explanation', 'Relevant examples', 'Professional delivery'],
            follow_up_hints=['Provide specific examples', 'Address practical concerns'],
            persona_context=f"This {context.persona_type.value} is seeking basic understanding of the proposal."
        )
