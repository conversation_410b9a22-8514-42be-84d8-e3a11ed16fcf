#!/usr/bin/env python3
"""
Test the complete AI-powered chatbot functionality
"""

import requests
import json
import time
from datetime import datetime

def test_ai_chatbot():
    """Test the AI-powered chatbot with various queries"""
    print("🤖 AI-POWERED MARKET PRICE CHATBOT TEST")
    print("=" * 60)
    print("Testing the complete AI integration with Gemini 2.5 Flash")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test health endpoint first
    try:
        health_response = requests.get(f"{base_url}/api/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ Chatbot server is running")
        else:
            print("❌ Chatbot server health check failed")
            return
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to chatbot server. Make sure it's running with: python app.py")
        return
    
    # Test queries that showcase AI capabilities
    ai_test_queries = [
        {
            "query": "ERCOT LMP statistics this week",
            "description": "Basic ISO and data type recognition"
        },
        {
            "query": "What is the RMSE for NDDAM prices in PJM north hub?",
            "description": "Complex query with specific metric, data type, and hub"
        },
        {
            "query": "Show me volatility for CAISO real-time prices",
            "description": "Analysis type recognition with RT abbreviation"
        },
        {
            "query": "Calculate MAPE for MISO bal day prices last month",
            "description": "Advanced metric with balance day and time period"
        },
        {
            "query": "RT price trends for NYISO this week",
            "description": "Trend analysis with real-time abbreviation"
        },
        {
            "query": "Compare peak vs off-peak pricing for ERCOT",
            "description": "Peak analysis request"
        },
        {
            "query": "What's the average price difference between DA and RT for PJM?",
            "description": "Complex comparison query"
        }
    ]
    
    successful_tests = 0
    total_tests = len(ai_test_queries)
    
    for i, test_case in enumerate(ai_test_queries, 1):
        print(f"\n🔍 Test {i}/{total_tests}: {test_case['description']}")
        print(f"Query: '{test_case['query']}'")
        print("-" * 50)
        
        try:
            # Send request to chatbot
            chat_data = {"message": test_case['query']}
            
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/chat",
                json=chat_data,
                timeout=30
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Response received in {response_time:.2f}s")
                
                # Check if we got a meaningful response
                if 'response' in result and len(result['response']) > 50:
                    print("📊 AI Response Preview:")
                    response_lines = result['response'].split('\n')
                    for line in response_lines[:4]:  # Show first 4 lines
                        if line.strip():
                            print(f"   {line[:80]}{'...' if len(line) > 80 else ''}")
                    
                    # Show additional info if available
                    if result.get('data_summary'):
                        data_summary = result['data_summary']
                        print(f"📈 Data: {data_summary.get('data_points', 'N/A')} points")
                        if data_summary.get('sensor_description'):
                            print(f"🎯 Sensor: {data_summary['sensor_description'][:60]}...")
                    
                    if result.get('confidence'):
                        confidence = result['confidence']
                        print(f"🎯 AI Confidence: {confidence:.1%}")
                    
                    successful_tests += 1
                    
                else:
                    print("⚠️ Response too short or missing")
                    print(f"   Response: {result.get('response', 'No response')[:100]}...")
                    
            else:
                print(f"❌ HTTP Error {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print("⏰ Request timed out (>30s)")
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        # Small delay between requests
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print("🎉 AI CHATBOT TEST SUMMARY")
    print("=" * 60)
    
    success_rate = (successful_tests / total_tests) * 100
    
    print(f"✅ Successful responses: {successful_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 EXCELLENT! Your AI chatbot is working perfectly!")
        print("\n🚀 Key AI Features Demonstrated:")
        print("   🧠 Natural language understanding with Gemini 2.5 Flash")
        print("   🎯 Intelligent sensor discovery and matching")
        print("   📊 Dynamic analysis generation")
        print("   🔍 Complex query parsing (ISOs, data types, hubs, metrics)")
        print("   ⚡ Real-time processing and response generation")
        
    elif success_rate >= 60:
        print("✅ GOOD! Most features are working, some fine-tuning needed")
        
    else:
        print("⚠️ NEEDS IMPROVEMENT! Check the error messages above")
    
    print(f"\n🌐 Web Interface: {base_url}")
    print("💡 Try these example queries in the web interface:")
    print("   • 'ERCOT NDDAM price volatility this week'")
    print("   • 'Calculate RMSE for PJM LMP north hub'")
    print("   • 'Show me RT price trends for CAISO'")
    print("   • 'What's the peak premium for NYISO?'")

def test_specific_ai_features():
    """Test specific AI features"""
    print("\n\n🔬 TESTING SPECIFIC AI FEATURES")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test abbreviation understanding
    abbreviation_tests = [
        "RT prices for ERCOT",  # Real-time
        "DA market for PJM",    # Day-ahead
        "NDDAM for CAISO",      # Next day day-ahead market
        "bal day for MISO"      # Balance day
    ]
    
    print("🔤 Testing abbreviation understanding:")
    for query in abbreviation_tests:
        try:
            response = requests.post(
                f"{base_url}/api/chat",
                json={"message": query},
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                if len(result.get('response', '')) > 30:
                    print(f"   ✅ '{query}' → Understood")
                else:
                    print(f"   ❌ '{query}' → Not understood")
            else:
                print(f"   ❌ '{query}' → Error {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ '{query}' → Exception: {str(e)}")
        
        time.sleep(0.5)
    
    # Test complex calculations
    print("\n🧮 Testing complex calculation requests:")
    complex_queries = [
        "Calculate weekly MAPE for ERCOT using 48-hour moving average",
        "Compare volatility between peak and off-peak hours for PJM",
        "Show me the correlation between DA and RT prices for CAISO"
    ]
    
    for query in complex_queries:
        try:
            response = requests.post(
                f"{base_url}/api/chat",
                json={"message": query},
                timeout=20
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'analysis' in result.get('response', '').lower() or 'calculate' in result.get('response', '').lower():
                    print(f"   ✅ Complex calculation handled")
                else:
                    print(f"   ⚠️ Basic response provided")
            else:
                print(f"   ❌ Error {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
        
        time.sleep(1)

def main():
    """Run all tests"""
    print(f"🚀 AI CHATBOT COMPREHENSIVE TEST SUITE")
    print(f"Started at: {datetime.now()}")
    
    try:
        # Test main chatbot functionality
        test_ai_chatbot()
        
        # Test specific AI features
        test_specific_ai_features()
        
        print("\n\n🎊 ALL TESTS COMPLETED!")
        print("Your AI-powered market price chatbot is ready for production use!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Test suite failed: {str(e)}")

if __name__ == "__main__":
    main()
