<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Training Assistant - Market Analysis & Roleplay</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .chat-container {
            height: calc(100vh - 200px);
        }
        .message-bubble {
            max-width: 80%;
            word-wrap: break-word;
        }
        .typing-indicator {
            display: none;
        }
        .typing-indicator.show {
            display: flex;
        }
        .dot {
            height: 8px;
            width: 8px;
            border-radius: 50%;
            background-color: #9CA3AF;
            animation: typing 1.4s infinite ease-in-out;
        }
        .dot:nth-child(1) { animation-delay: -0.32s; }
        .dot:nth-child(2) { animation-delay: -0.16s; }
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .chart-container {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
        }
        .mode-tab {
            transition: all 0.3s ease;
        }
        .mode-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .persona-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .persona-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .persona-card.selected {
            border: 2px solid #667eea;
            background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
        }
        .feedback-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .score-circle {
            background: conic-gradient(from 0deg, #10b981 0deg, #10b981 calc(var(--score) * 3.6deg), #e5e7eb calc(var(--score) * 3.6deg), #e5e7eb 360deg);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800">
                        <i class="fas fa-brain text-blue-600 mr-3"></i>
                        AI Training Assistant
                    </h1>
                    <p class="text-gray-600 mt-2">Market analysis chatbot and roleplay training for client-facing teams</p>

                    <!-- Mode Switcher -->
                    <div class="flex mt-4 space-x-2">
                        <button id="analysis-mode-btn" class="mode-tab active px-4 py-2 rounded-lg font-medium text-sm">
                            <i class="fas fa-chart-line mr-2"></i>Market Analysis
                        </button>
                        <button id="roleplay-mode-btn" class="mode-tab px-4 py-2 rounded-lg font-medium text-sm bg-gray-200 text-gray-700">
                            <i class="fas fa-users mr-2"></i>Roleplay Training
                        </button>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Status</div>
                    <div id="status" class="text-green-600 font-semibold">
                        <i class="fas fa-circle text-xs mr-1"></i>Online
                    </div>
                </div>
            </div>
        </div>

        <!-- Analysis Mode Content -->
        <div id="analysis-content" class="mode-content">
            <!-- Example Queries -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                    Example Queries
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <button class="example-query bg-blue-50 hover:bg-blue-100 p-3 rounded-lg text-left transition-colors">
                        <div class="text-sm font-medium text-blue-800">NDDAM Price Query</div>
                        <div class="text-xs text-blue-600 mt-1">"NDDAM price of ERCOT this week"</div>
                    </button>
                    <button class="example-query bg-green-50 hover:bg-green-100 p-3 rounded-lg text-left transition-colors">
                        <div class="text-sm font-medium text-green-800">RMSE Analysis</div>
                        <div class="text-xs text-green-600 mt-1">"What is the RMSE for ERCOT LMP North Hub?"</div>
                    </button>
                    <button class="example-query bg-purple-50 hover:bg-purple-100 p-3 rounded-lg text-left transition-colors">
                        <div class="text-sm font-medium text-purple-800">Volatility Analysis</div>
                        <div class="text-xs text-purple-600 mt-1">"Show me volatility for PJM day-ahead prices"</div>
                    </button>
                    <button class="example-query bg-orange-50 hover:bg-orange-100 p-3 rounded-lg text-left transition-colors">
                        <div class="text-sm font-medium text-orange-800">Load Analysis</div>
                        <div class="text-xs text-orange-600 mt-1">"Analyze CAISO load trends last month"</div>
                    </button>
                    <button class="example-query bg-red-50 hover:bg-red-100 p-3 rounded-lg text-left transition-colors">
                        <div class="text-sm font-medium text-red-800">Real-time Prices</div>
                        <div class="text-xs text-red-600 mt-1">"Real-time LMP statistics for NYISO"</div>
                    </button>
                    <button class="example-query bg-indigo-50 hover:bg-indigo-100 p-3 rounded-lg text-left transition-colors">
                        <div class="text-sm font-medium text-indigo-800">Peak Analysis</div>
                        <div class="text-xs text-indigo-600 mt-1">"Peak hours analysis for MISO this week"</div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Roleplay Mode Content -->
        <div id="roleplay-content" class="mode-content hidden">
            <!-- Roleplay Setup -->
            <div id="roleplay-setup" class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-play-circle text-green-500 mr-2"></i>
                    Start Roleplay Training Session
                </h3>

                <!-- Persona Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Select Persona Type</label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="persona-card bg-white border-2 border-gray-200 rounded-lg p-4" data-persona="client">
                            <div class="text-center">
                                <i class="fas fa-building text-3xl text-blue-600 mb-2"></i>
                                <h4 class="font-semibold text-gray-800">Client</h4>
                                <p class="text-sm text-gray-600 mt-1">Decision-maker focused on business value and ROI</p>
                            </div>
                        </div>
                        <div class="persona-card bg-white border-2 border-gray-200 rounded-lg p-4" data-persona="vendor">
                            <div class="text-center">
                                <i class="fas fa-handshake text-3xl text-green-600 mb-2"></i>
                                <h4 class="font-semibold text-gray-800">Vendor</h4>
                                <p class="text-sm text-gray-600 mt-1">Solution provider seeking partnership opportunities</p>
                            </div>
                        </div>
                        <div class="persona-card bg-white border-2 border-gray-200 rounded-lg p-4" data-persona="consultant">
                            <div class="text-center">
                                <i class="fas fa-user-tie text-3xl text-purple-600 mb-2"></i>
                                <h4 class="font-semibold text-gray-800">Consultant</h4>
                                <p class="text-sm text-gray-600 mt-1">Strategic advisor evaluating best practices</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mood Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Select Mood</label>
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
                        <button class="mood-btn bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg p-3 text-center transition-colors" data-mood="curious">
                            <i class="fas fa-search text-blue-600 mb-1"></i>
                            <div class="text-sm font-medium text-blue-800">Curious</div>
                        </button>
                        <button class="mood-btn bg-red-50 hover:bg-red-100 border border-red-200 rounded-lg p-3 text-center transition-colors" data-mood="skeptical">
                            <i class="fas fa-question-circle text-red-600 mb-1"></i>
                            <div class="text-sm font-medium text-red-800">Skeptical</div>
                        </button>
                        <button class="mood-btn bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-3 text-center transition-colors" data-mood="indifferent">
                            <i class="fas fa-meh text-gray-600 mb-1"></i>
                            <div class="text-sm font-medium text-gray-800">Indifferent</div>
                        </button>
                        <button class="mood-btn bg-yellow-50 hover:bg-yellow-100 border border-yellow-200 rounded-lg p-3 text-center transition-colors" data-mood="passive">
                            <i class="fas fa-clock text-yellow-600 mb-1"></i>
                            <div class="text-sm font-medium text-yellow-800">Passive</div>
                        </button>
                        <button class="mood-btn bg-orange-50 hover:bg-orange-100 border border-orange-200 rounded-lg p-3 text-center transition-colors" data-mood="not_interested">
                            <i class="fas fa-times-circle text-orange-600 mb-1"></i>
                            <div class="text-sm font-medium text-orange-800">Not Interested</div>
                        </button>
                    </div>
                </div>

                <!-- Agenda Input -->
                <div class="mb-6">
                    <label for="agenda-input" class="block text-sm font-medium text-gray-700 mb-2">Meeting Agenda</label>
                    <input type="text" id="agenda-input" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="e.g., Energy Transition with Tesla, Digital Transformation Strategy, Risk Management Solutions">
                </div>

                <!-- Industry Selection -->
                <div class="mb-6">
                    <label for="industry-select" class="block text-sm font-medium text-gray-700 mb-2">Industry Context</label>
                    <select id="industry-select" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="energy">Energy & Utilities</option>
                        <option value="technology">Technology</option>
                        <option value="financial">Financial Services</option>
                    </select>
                </div>

                <!-- Start Button -->
                <button id="start-roleplay-btn" class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-play mr-2"></i>Start Roleplay Session
                </button>
            </div>

            <!-- Active Session Interface -->
            <div id="roleplay-session" class="hidden">
                <!-- Session Info -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div id="session-persona" class="flex items-center">
                                <i class="fas fa-user-circle text-2xl text-blue-600 mr-2"></i>
                                <div>
                                    <div class="font-semibold text-gray-800">Client - Skeptical</div>
                                    <div class="text-sm text-gray-600">Energy Transition Discussion</div>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div id="session-progress" class="text-sm text-gray-600">
                                Question 1 of 5
                            </div>
                            <button id="end-session-btn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                                <i class="fas fa-stop mr-1"></i>End Session
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Coaching Tips -->
                <div id="coaching-tips" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-blue-800 mb-2">
                        <i class="fas fa-lightbulb mr-2"></i>Coaching Tips
                    </h4>
                    <ul id="tips-list" class="text-sm text-blue-700 space-y-1">
                        <!-- Tips will be populated dynamically -->
                    </ul>
                </div>
            </div>
        </div>

        <!-- Chat Interface -->
        <div class="bg-white rounded-lg shadow-md">
            <!-- Chat Messages -->
            <div id="chat-messages" class="chat-container overflow-y-auto p-6 space-y-4">
                <!-- Welcome Message -->
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-robot text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="message-bubble bg-blue-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-blue-800 mb-1">Market Analysis Bot</div>
                        <div class="text-blue-700">
                            Hello! I'm your market price analysis assistant. I can help you analyze electricity market data from various ISOs using natural language queries.

                            <div class="mt-3">
                                <strong>Supported Data Types:</strong>
                                <ul class="mt-1 space-y-1 text-sm">
                                    <li>• LMP (Locational Marginal Prices)</li>
                                    <li>• NDDAM (Next Day Day-Ahead Market)</li>
                                    <li>• Real-time prices</li>
                                    <li>• Load/Demand data</li>
                                    <li>• Generation data</li>
                                </ul>
                            </div>

                            <div class="mt-3">
                                <strong>Analysis Types:</strong>
                                <ul class="mt-1 space-y-1 text-sm">
                                    <li>• RMSE and MAE calculations</li>
                                    <li>• Volatility analysis</li>
                                    <li>• Price trend analysis</li>
                                    <li>• Peak hours analysis</li>
                                    <li>• Statistical summaries</li>
                                    <li>• Price forecasting</li>
                                </ul>
                            </div>

                            <div class="mt-3 text-sm">
                                Try asking: <em>"NDDAM price of ERCOT this week"</em> or <em>"What is the RMSE for PJM LMP?"</em>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Typing Indicator -->
            <div id="typing-indicator" class="typing-indicator px-6 pb-4">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center">
                            <i class="fas fa-robot text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="bg-gray-100 p-4 rounded-lg">
                        <div class="flex space-x-1">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Input Area -->
            <div class="border-t border-gray-200 p-6">
                <div class="flex space-x-4">
                    <div class="flex-1">
                        <input
                            type="text"
                            id="message-input"
                            placeholder="e.g., 'NDDAM price of ERCOT', 'RMSE for PJM LMP', 'CAISO load volatility'..."
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            maxlength="500"
                        >
                    </div>
                    <button 
                        id="send-button" 
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                    >
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    Supported ISOs: ERCOT, PJM, CAISO, NYISO, ISO-NE, MISO, SPP | Data Types: LMP, NDDAM, RT, Load, Generation
                </div>
            </div>
        </div>
    </div>

    <script>
        class ChatBot {
            constructor() {
                this.messagesContainer = document.getElementById('chat-messages');
                this.messageInput = document.getElementById('message-input');
                this.sendButton = document.getElementById('send-button');
                this.typingIndicator = document.getElementById('typing-indicator');
                this.status = document.getElementById('status');
                
                this.initializeEventListeners();
                this.checkHealth();
            }

            initializeEventListeners() {
                // Send message on button click
                this.sendButton.addEventListener('click', () => this.sendMessage());
                
                // Send message on Enter key
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.sendMessage();
                    }
                });

                // Example query buttons
                document.querySelectorAll('.example-query').forEach(button => {
                    button.addEventListener('click', () => {
                        const queryText = button.querySelector('.text-xs').textContent.replace(/"/g, '');
                        this.messageInput.value = queryText;
                        this.sendMessage();
                    });
                });
            }

            async checkHealth() {
                try {
                    const response = await fetch('/api/health');
                    if (response.ok) {
                        this.updateStatus('online', 'Online');
                    } else {
                        this.updateStatus('offline', 'Offline');
                    }
                } catch (error) {
                    this.updateStatus('offline', 'Offline');
                }
            }

            updateStatus(status, text) {
                const statusElement = this.status;
                statusElement.innerHTML = `<i class="fas fa-circle text-xs mr-1"></i>${text}`;
                
                if (status === 'online') {
                    statusElement.className = 'text-green-600 font-semibold';
                } else {
                    statusElement.className = 'text-red-600 font-semibold';
                }
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message) return;

                // Add user message to chat
                this.addMessage(message, 'user');
                this.messageInput.value = '';

                // Check if we're in roleplay mode
                const roleplayTrainer = window.roleplayTrainer;
                if (roleplayTrainer && roleplayTrainer.currentSession && roleplayTrainer.currentQuestion) {
                    // Handle roleplay response
                    this.showTypingIndicator();
                    await this.sendRoleplayResponse(message, roleplayTrainer.currentQuestion);
                    this.hideTypingIndicator();
                    return;
                }

                // Show typing indicator for regular chat
                this.showTypingIndicator();

                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ message: message })
                    });

                    const data = await response.json();

                    // Hide typing indicator
                    this.hideTypingIndicator();

                    if (response.ok) {
                        this.addMessage(data.response, 'bot', data.metrics, data.data_summary);
                    } else {
                        this.addMessage('Sorry, I encountered an error processing your request.', 'bot');
                    }
                } catch (error) {
                    this.hideTypingIndicator();
                    this.addMessage('Sorry, I\'m having trouble connecting. Please try again.', 'bot');
                }
            }

            addMessage(content, sender, metrics = null, dataSummary = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex items-start space-x-3';
                
                if (sender === 'user') {
                    messageDiv.className += ' justify-end';
                    messageDiv.innerHTML = `
                        <div class="message-bubble bg-blue-600 text-white p-4 rounded-lg">
                            <div class="whitespace-pre-wrap">${this.escapeHtml(content)}</div>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="message-bubble bg-gray-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-gray-800 mb-2">Market Analysis Bot</div>
                            <div class="text-gray-700 whitespace-pre-wrap">${this.formatBotResponse(content)}</div>
                            ${metrics ? this.createMetricsDisplay(metrics) : ''}
                            ${dataSummary ? this.createDataSummary(dataSummary) : ''}
                        </div>
                    `;
                }
                
                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
            }

            formatBotResponse(content) {
                // Convert markdown-like formatting to HTML
                return content
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/•/g, '•')
                    .replace(/📊|📈|⚡|🕐|🔮|💰/g, '<span class="text-lg">$&</span>');
            }

            createMetricsDisplay(metrics) {
                if (!metrics || Object.keys(metrics).length === 0) return '';
                
                let html = '<div class="mt-4 grid grid-cols-2 gap-2">';
                for (const [key, value] of Object.entries(metrics)) {
                    const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    const formattedValue = typeof value === 'number' ? value.toFixed(3) : value;
                    html += `
                        <div class="metric-card text-white p-3 rounded text-center">
                            <div class="text-xs opacity-90">${formattedKey}</div>
                            <div class="text-lg font-bold">${formattedValue}</div>
                        </div>
                    `;
                }
                html += '</div>';
                return html;
            }

            createDataSummary(dataSummary) {
                if (!dataSummary) return '';
                
                return `
                    <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                        <div class="text-xs font-medium text-blue-800 mb-2">Data Summary</div>
                        <div class="text-xs text-blue-700 space-y-1">
                            <div><strong>ISO:</strong> ${dataSummary.iso || 'N/A'}</div>
                            <div><strong>Hub:</strong> ${dataSummary.hub || 'N/A'}</div>
                            <div><strong>Data Points:</strong> ${dataSummary.data_points || 'N/A'}</div>
                            ${dataSummary.date_range ? `
                                <div><strong>Period:</strong> ${dataSummary.date_range.start} to ${dataSummary.date_range.end}</div>
                            ` : ''}
                        </div>
                    </div>
                `;
            }

            showTypingIndicator() {
                this.typingIndicator.classList.add('show');
                this.scrollToBottom();
            }

            hideTypingIndicator() {
                this.typingIndicator.classList.remove('show');
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                }, 100);
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // Roleplay-specific methods
            addRoleplayQuestion(question) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex items-start space-x-3';

                messageDiv.innerHTML = `
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user-tie text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="message-bubble bg-purple-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-purple-800 mb-2">
                            ${this.getPersonaTitle()} - ${question.difficulty_level.toUpperCase()}
                        </div>
                        <div class="text-purple-700 mb-3">${question.question_text}</div>
                        <div class="text-xs text-purple-600 bg-purple-100 p-2 rounded">
                            <strong>Context:</strong> ${question.persona_context}
                        </div>
                    </div>
                `;

                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
            }

            getPersonaTitle() {
                const trainer = window.roleplayTrainer;
                if (!trainer || !trainer.selectedPersona) return 'Persona';

                const titles = {
                    'client': 'Client Executive',
                    'vendor': 'Vendor Representative',
                    'consultant': 'Strategy Consultant'
                };

                return titles[trainer.selectedPersona] || 'Persona';
            }

            async sendRoleplayResponse(response, questionData) {
                if (!window.roleplayTrainer.currentSession) return;

                try {
                    const apiResponse = await fetch(`/api/roleplay/${window.roleplayTrainer.currentSession.session_id}/respond`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            response: response,
                            question_text: questionData.question_text,
                            question_type: questionData.question_type,
                            expected_topics: questionData.expected_topics,
                            industry: document.getElementById('industry-select').value
                        })
                    });

                    const data = await apiResponse.json();

                    if (apiResponse.ok) {
                        // Show feedback
                        this.displayFeedback(data.feedback);

                        // Update progress
                        const progress = data.session_progress;
                        document.getElementById('session-progress').textContent =
                            `Question ${progress.current_question} of ${progress.total_questions}`;

                        // Show next question or end session
                        if (data.next_question && !progress.session_complete) {
                            setTimeout(() => {
                                this.addRoleplayQuestion(data.next_question);
                                window.roleplayTrainer.currentQuestion = data.next_question;
                            }, 2000);
                        } else if (progress.session_complete) {
                            setTimeout(() => {
                                window.roleplayTrainer.endSession();
                            }, 3000);
                        }
                    } else {
                        this.addMessage('Error processing response: ' + (data.error || 'Unknown error'), 'bot');
                    }
                } catch (error) {
                    this.addMessage('Failed to process roleplay response: ' + error.message, 'bot');
                }
            }

            displayFeedback(feedback) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex items-start space-x-3';

                const scoreColor = feedback.overall_score >= 7 ? 'green' : feedback.overall_score >= 5 ? 'yellow' : 'red';

                messageDiv.innerHTML = `
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-clipboard-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="message-bubble bg-blue-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-blue-800 mb-2">
                            <i class="fas fa-star text-${scoreColor}-500 mr-1"></i>
                            Feedback - Score: ${feedback.overall_score}/10
                        </div>

                        ${feedback.strengths.length > 0 ? `
                            <div class="mb-3">
                                <div class="text-sm font-medium text-green-700 mb-1">✅ Strengths:</div>
                                <ul class="text-sm text-green-600 ml-4">
                                    ${feedback.strengths.map(s => `<li>• ${s}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}

                        ${feedback.improvement_areas.length > 0 ? `
                            <div class="mb-3">
                                <div class="text-sm font-medium text-orange-700 mb-1">🔄 Areas for Improvement:</div>
                                <ul class="text-sm text-orange-600 ml-4">
                                    ${feedback.improvement_areas.map(a => `<li>• ${a}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}

                        ${feedback.specific_suggestions.length > 0 ? `
                            <div class="mb-3">
                                <div class="text-sm font-medium text-blue-700 mb-1">💡 Suggestions:</div>
                                <ul class="text-sm text-blue-600 ml-4">
                                    ${feedback.specific_suggestions.map(s => `<li>• ${s}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>
                `;

                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
            }
        }

        class RoleplayTrainer {
            constructor() {
                this.currentSession = null;
                this.selectedPersona = null;
                this.selectedMood = null;
                this.currentQuestion = null;

                this.initializeEventListeners();
            }

            initializeEventListeners() {
                // Mode switching
                document.getElementById('analysis-mode-btn').addEventListener('click', () => this.switchMode('analysis'));
                document.getElementById('roleplay-mode-btn').addEventListener('click', () => this.switchMode('roleplay'));

                // Persona selection
                document.querySelectorAll('.persona-card').forEach(card => {
                    card.addEventListener('click', () => this.selectPersona(card));
                });

                // Mood selection
                document.querySelectorAll('.mood-btn').forEach(btn => {
                    btn.addEventListener('click', () => this.selectMood(btn));
                });

                // Start roleplay session
                document.getElementById('start-roleplay-btn').addEventListener('click', () => this.startRoleplaySession());

                // End session
                document.getElementById('end-session-btn').addEventListener('click', () => this.endSession());
            }

            switchMode(mode) {
                const analysisBtn = document.getElementById('analysis-mode-btn');
                const roleplayBtn = document.getElementById('roleplay-mode-btn');
                const analysisContent = document.getElementById('analysis-content');
                const roleplayContent = document.getElementById('roleplay-content');

                if (mode === 'analysis') {
                    analysisBtn.classList.add('active');
                    roleplayBtn.classList.remove('active');
                    analysisContent.classList.remove('hidden');
                    roleplayContent.classList.add('hidden');
                } else {
                    roleplayBtn.classList.add('active');
                    analysisBtn.classList.remove('active');
                    roleplayContent.classList.remove('hidden');
                    analysisContent.classList.add('hidden');
                }
            }

            selectPersona(card) {
                // Remove previous selection
                document.querySelectorAll('.persona-card').forEach(c => c.classList.remove('selected'));

                // Add selection to clicked card
                card.classList.add('selected');
                this.selectedPersona = card.dataset.persona;

                this.updateStartButton();
            }

            selectMood(btn) {
                // Remove previous selection
                document.querySelectorAll('.mood-btn').forEach(b => b.classList.remove('ring-2', 'ring-blue-500'));

                // Add selection to clicked button
                btn.classList.add('ring-2', 'ring-blue-500');
                this.selectedMood = btn.dataset.mood;

                this.updateStartButton();
            }

            updateStartButton() {
                const startBtn = document.getElementById('start-roleplay-btn');
                const agenda = document.getElementById('agenda-input').value.trim();

                if (this.selectedPersona && this.selectedMood && agenda) {
                    startBtn.disabled = false;
                } else {
                    startBtn.disabled = true;
                }
            }

            async startRoleplaySession() {
                const agenda = document.getElementById('agenda-input').value.trim();
                const industry = document.getElementById('industry-select').value;

                if (!this.selectedPersona || !this.selectedMood || !agenda) {
                    alert('Please select persona, mood, and enter an agenda');
                    return;
                }

                try {
                    const response = await fetch('/api/roleplay/create', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            persona_type: this.selectedPersona,
                            mood_type: this.selectedMood,
                            agenda: agenda,
                            industry: industry,
                            max_questions: 5
                        })
                    });

                    const data = await response.json();

                    if (response.ok) {
                        this.currentSession = data;
                        this.showSessionInterface(data);
                    } else {
                        alert('Error creating session: ' + (data.error || 'Unknown error'));
                    }
                } catch (error) {
                    alert('Failed to create roleplay session: ' + error.message);
                }
            }

            showSessionInterface(sessionData) {
                // Hide setup, show session
                document.getElementById('roleplay-setup').classList.add('hidden');
                document.getElementById('roleplay-session').classList.remove('hidden');

                // Update session info
                const personaInfo = document.getElementById('session-persona');
                const personaName = sessionData.persona_profile.name || this.selectedPersona;
                const moodName = sessionData.mood_profile.description || this.selectedMood;

                personaInfo.innerHTML = `
                    <i class="fas fa-user-circle text-2xl text-blue-600 mr-2"></i>
                    <div>
                        <div class="font-semibold text-gray-800">${personaName} - ${moodName}</div>
                        <div class="text-sm text-gray-600">${sessionData.context.scenario_description}</div>
                    </div>
                `;

                // Update progress
                document.getElementById('session-progress').textContent = 'Question 1 of 5';

                // Show coaching tips
                this.displayCoachingTips(sessionData.coaching_tips);

                // Display first question
                this.displayQuestion(sessionData.first_question);

                // Clear and setup chat for roleplay
                this.setupRoleplayChat();
            }

            displayCoachingTips(tips) {
                const tipsList = document.getElementById('tips-list');
                tipsList.innerHTML = '';

                tips.forEach(tip => {
                    const li = document.createElement('li');
                    li.textContent = `• ${tip}`;
                    tipsList.appendChild(li);
                });
            }

            displayQuestion(question) {
                this.currentQuestion = question;

                // Add question to chat
                const chatBot = window.chatBotInstance;
                if (chatBot) {
                    chatBot.addRoleplayQuestion(question);
                }
            }

            setupRoleplayChat() {
                // Clear existing messages except welcome
                const messagesContainer = document.getElementById('chat-messages');
                const welcomeMessage = messagesContainer.querySelector('.flex.items-start.space-x-3');
                messagesContainer.innerHTML = '';

                // Add roleplay welcome message
                const roleplayWelcome = document.createElement('div');
                roleplayWelcome.className = 'flex items-start space-x-3';
                roleplayWelcome.innerHTML = `
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-theater-masks text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="message-bubble bg-purple-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-purple-800 mb-1">Roleplay Trainer</div>
                        <div class="text-purple-700">
                            Welcome to your roleplay training session! I'll be playing the role of a ${this.selectedPersona}
                            who is ${this.selectedMood} about "${document.getElementById('agenda-input').value}".

                            <div class="mt-3 text-sm">
                                <strong>Your goal:</strong> Practice your presentation and communication skills.
                                I'll provide feedback after each response to help you improve.
                            </div>
                        </div>
                    </div>
                `;
                messagesContainer.appendChild(roleplayWelcome);
            }

            async endSession() {
                if (!this.currentSession) return;

                try {
                    const response = await fetch(`/api/roleplay/${this.currentSession.session_id}/complete`, {
                        method: 'POST'
                    });

                    const data = await response.json();

                    if (response.ok) {
                        this.showFinalEvaluation(data.evaluation);
                    } else {
                        alert('Error ending session: ' + (data.error || 'Unknown error'));
                    }
                } catch (error) {
                    alert('Failed to end session: ' + error.message);
                }

                // Reset interface
                this.resetInterface();
            }

            showFinalEvaluation(evaluation) {
                // Create evaluation modal or display
                const evalHtml = `
                    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                            <h3 class="text-xl font-bold text-gray-800 mb-4">
                                <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                                Session Evaluation
                            </h3>

                            <div class="mb-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">Overall Score</span>
                                    <span class="text-2xl font-bold text-blue-600">${evaluation.overall_score}/10</span>
                                </div>
                                <div class="text-sm text-gray-600">Confidence Level: ${evaluation.confidence_level}</div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <h4 class="font-semibold text-green-700 mb-2">Strengths</h4>
                                    <ul class="text-sm text-gray-600 space-y-1">
                                        ${evaluation.strengths.map(s => `<li>• ${s}</li>`).join('')}
                                    </ul>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-orange-700 mb-2">Areas for Improvement</h4>
                                    <ul class="text-sm text-gray-600 space-y-1">
                                        ${evaluation.improvement_areas.map(a => `<li>• ${a}</li>`).join('')}
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h4 class="font-semibold text-blue-700 mb-2">Recommendations</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    ${evaluation.recommendations.map(r => `<li>• ${r}</li>`).join('')}
                                </ul>
                            </div>

                            <button onclick="this.parentElement.parentElement.remove()"
                                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                                Close
                            </button>
                        </div>
                    </div>
                `;

                document.body.insertAdjacentHTML('beforeend', evalHtml);
            }

            resetInterface() {
                // Show setup, hide session
                document.getElementById('roleplay-setup').classList.remove('hidden');
                document.getElementById('roleplay-session').classList.add('hidden');

                // Reset selections
                document.querySelectorAll('.persona-card').forEach(c => c.classList.remove('selected'));
                document.querySelectorAll('.mood-btn').forEach(b => b.classList.remove('ring-2', 'ring-blue-500'));

                this.selectedPersona = null;
                this.selectedMood = null;
                this.currentSession = null;
                this.currentQuestion = null;

                // Clear form
                document.getElementById('agenda-input').value = '';
                document.getElementById('start-roleplay-btn').disabled = true;
            }
        }

        // Initialize both systems when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.chatBotInstance = new ChatBot();
            window.roleplayTrainer = new RoleplayTrainer();

            // Add agenda input listener
            document.getElementById('agenda-input').addEventListener('input', () => {
                window.roleplayTrainer.updateStartButton();
            });
        });
    </script>
</body>
</html>
