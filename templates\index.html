<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Market Price Analysis Chatbot</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .chat-container {
            height: calc(100vh - 200px);
        }
        .message-bubble {
            max-width: 80%;
            word-wrap: break-word;
        }
        .typing-indicator {
            display: none;
        }
        .typing-indicator.show {
            display: flex;
        }
        .dot {
            height: 8px;
            width: 8px;
            border-radius: 50%;
            background-color: #9CA3AF;
            animation: typing 1.4s infinite ease-in-out;
        }
        .dot:nth-child(1) { animation-delay: -0.32s; }
        .dot:nth-child(2) { animation-delay: -0.16s; }
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .chart-container {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800">
                        <i class="fas fa-chart-line text-blue-600 mr-3"></i>
                        Market Price Analysis Chatbot
                    </h1>
                    <p class="text-gray-600 mt-2">Ask me about market prices, RMSE calculations, volatility analysis, and more!</p>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Status</div>
                    <div id="status" class="text-green-600 font-semibold">
                        <i class="fas fa-circle text-xs mr-1"></i>Online
                    </div>
                </div>
            </div>
        </div>

        <!-- Example Queries -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                Example Queries
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <button class="example-query bg-blue-50 hover:bg-blue-100 p-3 rounded-lg text-left transition-colors">
                    <div class="text-sm font-medium text-blue-800">NDDAM Price Query</div>
                    <div class="text-xs text-blue-600 mt-1">"NDDAM price of ERCOT this week"</div>
                </button>
                <button class="example-query bg-green-50 hover:bg-green-100 p-3 rounded-lg text-left transition-colors">
                    <div class="text-sm font-medium text-green-800">RMSE Analysis</div>
                    <div class="text-xs text-green-600 mt-1">"What is the RMSE for ERCOT LMP North Hub?"</div>
                </button>
                <button class="example-query bg-purple-50 hover:bg-purple-100 p-3 rounded-lg text-left transition-colors">
                    <div class="text-sm font-medium text-purple-800">Volatility Analysis</div>
                    <div class="text-xs text-purple-600 mt-1">"Show me volatility for PJM day-ahead prices"</div>
                </button>
                <button class="example-query bg-orange-50 hover:bg-orange-100 p-3 rounded-lg text-left transition-colors">
                    <div class="text-sm font-medium text-orange-800">Load Analysis</div>
                    <div class="text-xs text-orange-600 mt-1">"Analyze CAISO load trends last month"</div>
                </button>
                <button class="example-query bg-red-50 hover:bg-red-100 p-3 rounded-lg text-left transition-colors">
                    <div class="text-sm font-medium text-red-800">Real-time Prices</div>
                    <div class="text-xs text-red-600 mt-1">"Real-time LMP statistics for NYISO"</div>
                </button>
                <button class="example-query bg-indigo-50 hover:bg-indigo-100 p-3 rounded-lg text-left transition-colors">
                    <div class="text-sm font-medium text-indigo-800">Peak Analysis</div>
                    <div class="text-xs text-indigo-600 mt-1">"Peak hours analysis for MISO this week"</div>
                </button>
            </div>
        </div>

        <!-- Chat Interface -->
        <div class="bg-white rounded-lg shadow-md">
            <!-- Chat Messages -->
            <div id="chat-messages" class="chat-container overflow-y-auto p-6 space-y-4">
                <!-- Welcome Message -->
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-robot text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="message-bubble bg-blue-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-blue-800 mb-1">Market Analysis Bot</div>
                        <div class="text-blue-700">
                            Hello! I'm your market price analysis assistant. I can help you analyze electricity market data from various ISOs using natural language queries.

                            <div class="mt-3">
                                <strong>Supported Data Types:</strong>
                                <ul class="mt-1 space-y-1 text-sm">
                                    <li>• LMP (Locational Marginal Prices)</li>
                                    <li>• NDDAM (Next Day Day-Ahead Market)</li>
                                    <li>• Real-time prices</li>
                                    <li>• Load/Demand data</li>
                                    <li>• Generation data</li>
                                </ul>
                            </div>

                            <div class="mt-3">
                                <strong>Analysis Types:</strong>
                                <ul class="mt-1 space-y-1 text-sm">
                                    <li>• RMSE and MAE calculations</li>
                                    <li>• Volatility analysis</li>
                                    <li>• Price trend analysis</li>
                                    <li>• Peak hours analysis</li>
                                    <li>• Statistical summaries</li>
                                    <li>• Price forecasting</li>
                                </ul>
                            </div>

                            <div class="mt-3 text-sm">
                                Try asking: <em>"NDDAM price of ERCOT this week"</em> or <em>"What is the RMSE for PJM LMP?"</em>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Typing Indicator -->
            <div id="typing-indicator" class="typing-indicator px-6 pb-4">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center">
                            <i class="fas fa-robot text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="bg-gray-100 p-4 rounded-lg">
                        <div class="flex space-x-1">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Input Area -->
            <div class="border-t border-gray-200 p-6">
                <div class="flex space-x-4">
                    <div class="flex-1">
                        <input
                            type="text"
                            id="message-input"
                            placeholder="e.g., 'NDDAM price of ERCOT', 'RMSE for PJM LMP', 'CAISO load volatility'..."
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            maxlength="500"
                        >
                    </div>
                    <button 
                        id="send-button" 
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                    >
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    Supported ISOs: ERCOT, PJM, CAISO, NYISO, ISO-NE, MISO, SPP | Data Types: LMP, NDDAM, RT, Load, Generation
                </div>
            </div>
        </div>
    </div>

    <script>
        class ChatBot {
            constructor() {
                this.messagesContainer = document.getElementById('chat-messages');
                this.messageInput = document.getElementById('message-input');
                this.sendButton = document.getElementById('send-button');
                this.typingIndicator = document.getElementById('typing-indicator');
                this.status = document.getElementById('status');
                
                this.initializeEventListeners();
                this.checkHealth();
            }

            initializeEventListeners() {
                // Send message on button click
                this.sendButton.addEventListener('click', () => this.sendMessage());
                
                // Send message on Enter key
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.sendMessage();
                    }
                });

                // Example query buttons
                document.querySelectorAll('.example-query').forEach(button => {
                    button.addEventListener('click', () => {
                        const queryText = button.querySelector('.text-xs').textContent.replace(/"/g, '');
                        this.messageInput.value = queryText;
                        this.sendMessage();
                    });
                });
            }

            async checkHealth() {
                try {
                    const response = await fetch('/api/health');
                    if (response.ok) {
                        this.updateStatus('online', 'Online');
                    } else {
                        this.updateStatus('offline', 'Offline');
                    }
                } catch (error) {
                    this.updateStatus('offline', 'Offline');
                }
            }

            updateStatus(status, text) {
                const statusElement = this.status;
                statusElement.innerHTML = `<i class="fas fa-circle text-xs mr-1"></i>${text}`;
                
                if (status === 'online') {
                    statusElement.className = 'text-green-600 font-semibold';
                } else {
                    statusElement.className = 'text-red-600 font-semibold';
                }
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message) return;

                // Add user message to chat
                this.addMessage(message, 'user');
                this.messageInput.value = '';
                
                // Show typing indicator
                this.showTypingIndicator();
                
                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ message: message })
                    });

                    const data = await response.json();
                    
                    // Hide typing indicator
                    this.hideTypingIndicator();
                    
                    if (response.ok) {
                        this.addMessage(data.response, 'bot', data.metrics, data.data_summary);
                    } else {
                        this.addMessage('Sorry, I encountered an error processing your request.', 'bot');
                    }
                } catch (error) {
                    this.hideTypingIndicator();
                    this.addMessage('Sorry, I\'m having trouble connecting. Please try again.', 'bot');
                }
            }

            addMessage(content, sender, metrics = null, dataSummary = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex items-start space-x-3';
                
                if (sender === 'user') {
                    messageDiv.className += ' justify-end';
                    messageDiv.innerHTML = `
                        <div class="message-bubble bg-blue-600 text-white p-4 rounded-lg">
                            <div class="whitespace-pre-wrap">${this.escapeHtml(content)}</div>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="message-bubble bg-gray-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-gray-800 mb-2">Market Analysis Bot</div>
                            <div class="text-gray-700 whitespace-pre-wrap">${this.formatBotResponse(content)}</div>
                            ${metrics ? this.createMetricsDisplay(metrics) : ''}
                            ${dataSummary ? this.createDataSummary(dataSummary) : ''}
                        </div>
                    `;
                }
                
                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
            }

            formatBotResponse(content) {
                // Convert markdown-like formatting to HTML
                return content
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/•/g, '•')
                    .replace(/📊|📈|⚡|🕐|🔮|💰/g, '<span class="text-lg">$&</span>');
            }

            createMetricsDisplay(metrics) {
                if (!metrics || Object.keys(metrics).length === 0) return '';
                
                let html = '<div class="mt-4 grid grid-cols-2 gap-2">';
                for (const [key, value] of Object.entries(metrics)) {
                    const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    const formattedValue = typeof value === 'number' ? value.toFixed(3) : value;
                    html += `
                        <div class="metric-card text-white p-3 rounded text-center">
                            <div class="text-xs opacity-90">${formattedKey}</div>
                            <div class="text-lg font-bold">${formattedValue}</div>
                        </div>
                    `;
                }
                html += '</div>';
                return html;
            }

            createDataSummary(dataSummary) {
                if (!dataSummary) return '';
                
                return `
                    <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                        <div class="text-xs font-medium text-blue-800 mb-2">Data Summary</div>
                        <div class="text-xs text-blue-700 space-y-1">
                            <div><strong>ISO:</strong> ${dataSummary.iso || 'N/A'}</div>
                            <div><strong>Hub:</strong> ${dataSummary.hub || 'N/A'}</div>
                            <div><strong>Data Points:</strong> ${dataSummary.data_points || 'N/A'}</div>
                            ${dataSummary.date_range ? `
                                <div><strong>Period:</strong> ${dataSummary.date_range.start} to ${dataSummary.date_range.end}</div>
                            ` : ''}
                        </div>
                    </div>
                `;
            }

            showTypingIndicator() {
                this.typingIndicator.classList.add('show');
                this.scrollToBottom();
            }

            hideTypingIndicator() {
                this.typingIndicator.classList.remove('show');
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                }, 100);
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // Initialize the chatbot when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ChatBot();
        });
    </script>
</body>
</html>
