import json
from typing import Dict, List, Optional, Any
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ResponseGenerator:
    """Service for generating natural language responses with scientific data"""
    
    def __init__(self):
        self.response_templates = {
            'rmse': self._generate_rmse_response,
            'mae': self._generate_mae_response,
            'volatility': self._generate_volatility_response,
            'moving_average': self._generate_moving_average_response,
            'price_trend': self._generate_trend_response,
            'peak_analysis': self._generate_peak_analysis_response,
            'correlation': self._generate_correlation_response,
            'statistics': self._generate_statistics_response,
            'forecast': self._generate_forecast_response
        }
    
    def generate_response(self, query_params: Dict[str, Any], 
                         analysis_result: Dict[str, Any], 
                         original_query: str) -> Dict[str, Any]:
        """
        Generate a natural language response with scientific data
        
        Args:
            query_params: Parsed query parameters
            analysis_result: Results from data analysis
            original_query: Original user query
        
        Returns:
            Dictionary with response text, charts, and data summary
        """
        try:
            if 'error' in analysis_result:
                return {
                    'text': f"I encountered an issue: {analysis_result['error']}. Please check your query and try again.",
                    'charts': [],
                    'data_summary': {}
                }
            
            analysis_type = query_params.get('analysis_type', 'statistics')
            iso = query_params.get('iso', 'Unknown ISO')
            hub = query_params.get('hub', '')
            
            # Generate response based on analysis type
            if analysis_type in self.response_templates:
                response = self.response_templates[analysis_type](
                    query_params, analysis_result, original_query
                )
            else:
                # Handle comprehensive analysis
                response = self._generate_comprehensive_response(
                    query_params, analysis_result, original_query
                )
            
            # Add context information
            response['data_summary'] = {
                'iso': iso.upper(),
                'hub': hub.upper() if hub else 'All Hubs',
                'analysis_type': analysis_type,
                'data_points': analysis_result.get('data_points', 'Unknown'),
                'date_range': analysis_result.get('date_range', {})
            }
            
            return response

        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return {
                'text': f"I encountered an error while generating the response. Please try again.",
                'charts': [],
                'data_summary': {}
            }

    def generate_ai_response(self, query_params: Dict[str, Any], analysis_result: Dict[str, Any],
                           sensor_info: Dict[str, Any], original_query: str) -> str:
        """
        Generate natural language response from AI analysis results

        Args:
            query_params: Parsed query parameters from AI
            analysis_result: Analysis results from AI
            sensor_info: Information about the sensor used
            original_query: Original user query

        Returns:
            Natural language response string
        """
        try:
            # Extract key information
            iso = query_params.get('iso', 'Unknown').upper()
            data_type = query_params.get('data_type', 'market data')
            analysis_type = query_params.get('analysis_type', 'analysis')
            sensor_description = sensor_info.get('description', 'market sensor')

            # Start building response
            response_parts = []

            # Header with query understanding
            response_parts.append(f"📊 **Analysis Results for {iso} {data_type.upper()}**")
            response_parts.append(f"*Based on: {sensor_description[:100]}...*")
            response_parts.append("")

            # Add analysis results dynamically based on what AI calculated
            if isinstance(analysis_result, dict):
                for key, value in analysis_result.items():
                    if key == 'error':
                        continue

                    # Format different types of metrics
                    if 'rmse' in key.lower():
                        response_parts.append(f"🎯 **Root Mean Square Error**: {value:.3f} $/MWh")
                    elif 'mae' in key.lower():
                        response_parts.append(f"📏 **Mean Absolute Error**: {value:.3f} $/MWh")
                    elif 'mape' in key.lower():
                        response_parts.append(f"📊 **Mean Absolute Percentage Error**: {value:.2f}%")
                    elif 'volatility' in key.lower():
                        response_parts.append(f"📈 **Volatility**: {value:.2f}%")
                    elif 'mean' in key.lower() or 'average' in key.lower():
                        response_parts.append(f"💰 **Average Price**: ${value:.2f}/MWh")
                    elif 'std' in key.lower() or 'deviation' in key.lower():
                        response_parts.append(f"📊 **Standard Deviation**: ${value:.2f}/MWh")
                    elif 'max' in key.lower():
                        response_parts.append(f"⬆️ **Maximum**: ${value:.2f}/MWh")
                    elif 'min' in key.lower():
                        response_parts.append(f"⬇️ **Minimum**: ${value:.2f}/MWh")
                    elif 'trend' in key.lower():
                        trend_emoji = "📈" if value > 0 else "📉" if value < 0 else "➡️"
                        response_parts.append(f"{trend_emoji} **Price Trend**: {value:.3f} $/MWh per hour")
                    elif 'correlation' in key.lower():
                        response_parts.append(f"🔗 **Correlation**: {value:.3f}")
                    elif 'count' in key.lower() or 'points' in key.lower():
                        response_parts.append(f"📊 **Data Points**: {value:,}")
                    else:
                        # Generic formatting for other metrics
                        if isinstance(value, (int, float)):
                            if abs(value) < 1:
                                response_parts.append(f"📊 **{key.replace('_', ' ').title()}**: {value:.4f}")
                            elif abs(value) < 100:
                                response_parts.append(f"📊 **{key.replace('_', ' ').title()}**: {value:.2f}")
                            else:
                                response_parts.append(f"📊 **{key.replace('_', ' ').title()}**: {value:,.0f}")
                        else:
                            response_parts.append(f"📊 **{key.replace('_', ' ').title()}**: {value}")

            response_parts.append("")

            # Add interpretation based on analysis type
            if 'rmse' in analysis_type.lower():
                response_parts.append("🔍 **Interpretation**: RMSE measures prediction accuracy. Lower values indicate better forecasting performance.")
            elif 'volatility' in analysis_type.lower():
                response_parts.append("🔍 **Interpretation**: Higher volatility indicates more price fluctuation and market uncertainty.")
            elif 'mape' in analysis_type.lower():
                response_parts.append("🔍 **Interpretation**: MAPE shows percentage-based prediction error. Values under 10% are considered good.")
            elif 'trend' in analysis_type.lower():
                response_parts.append("🔍 **Interpretation**: Positive trends indicate rising prices, negative trends show declining prices.")
            elif 'peak' in analysis_type.lower():
                response_parts.append("🔍 **Interpretation**: Peak analysis compares high-demand vs low-demand period pricing.")

            # Add confidence indicator
            confidence = query_params.get('confidence', 0)
            if confidence > 0.8:
                response_parts.append(f"\n✅ **High confidence** in query interpretation ({confidence:.1%})")
            elif confidence > 0.6:
                response_parts.append(f"\n⚠️ **Medium confidence** in query interpretation ({confidence:.1%})")
            else:
                response_parts.append(f"\n❓ **Lower confidence** in query interpretation ({confidence:.1%}) - please verify results")

            return "\n".join(response_parts)

        except Exception as e:
            logger.error(f"Error generating AI response: {str(e)}")
            return f"Analysis completed for {query_params.get('iso', 'market')} {query_params.get('data_type', 'data')}, but I encountered an issue formatting the response. Raw results: {str(analysis_result)[:200]}..."
            
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return {
                'text': "I encountered an error while generating the response. Please try again.",
                'charts': [],
                'data_summary': {}
            }
    
    def _generate_rmse_response(self, query_params: Dict, analysis_result: Dict, original_query: str) -> Dict:
        """Generate response for RMSE analysis"""
        iso = query_params.get('iso', '').upper()
        hub = query_params.get('hub', '')
        hub_text = f" {hub.upper()} hub" if hub else ""
        
        rmse = analysis_result.get('rmse', 0)
        mae = analysis_result.get('mae', 0)
        mape = analysis_result.get('mape', 0)
        baseline = analysis_result.get('baseline_type', 'moving average')
        period = analysis_result.get('analysis_period', 'the analysis period')
        
        response_text = f"""
Based on my analysis of {iso}{hub_text} market prices, here are the key error metrics:

📊 **Root Mean Square Error (RMSE): ${rmse:.3f}/MWh**

This RMSE value indicates the average prediction error when using a {baseline} as the baseline. 

**Additional Error Metrics:**
• Mean Absolute Error (MAE): ${mae:.3f}/MWh
• Mean Absolute Percentage Error (MAPE): {mape:.2f}%

**Interpretation:**
"""
        
        if rmse < 5:
            response_text += "• **Low volatility** - Prices are relatively stable and predictable"
        elif rmse < 15:
            response_text += "• **Moderate volatility** - Typical market fluctuations observed"
        else:
            response_text += "• **High volatility** - Significant price variations detected"
        
        response_text += f"\n• Analysis covers {period} of market data"
        response_text += f"\n• Lower RMSE values indicate more predictable price patterns"
        
        return {
            'text': response_text,
            'charts': [],
            'metrics': {
                'rmse': rmse,
                'mae': mae,
                'mape': mape
            }
        }
    
    def _generate_volatility_response(self, query_params: Dict, analysis_result: Dict, original_query: str) -> Dict:
        """Generate response for volatility analysis"""
        iso = query_params.get('iso', '').upper()
        hub = query_params.get('hub', '')
        hub_text = f" {hub.upper()} hub" if hub else ""
        
        daily_vol = analysis_result.get('daily_volatility', 0)
        annual_vol = analysis_result.get('annualized_volatility', 0)
        price_range = analysis_result.get('price_range', 0)
        cv = analysis_result.get('coefficient_of_variation', 0)
        max_price = analysis_result.get('max_price', 0)
        min_price = analysis_result.get('min_price', 0)
        
        response_text = f"""
Here's the volatility analysis for {iso}{hub_text} market prices:

📈 **Volatility Metrics:**
• Daily Volatility: {daily_vol:.3f}%
• Annualized Volatility: {annual_vol:.2f}%
• Coefficient of Variation: {cv:.3f}

💰 **Price Range Analysis:**
• Maximum Price: ${max_price:.2f}/MWh
• Minimum Price: ${min_price:.2f}/MWh
• Price Range: ${price_range:.2f}/MWh

**Market Assessment:**
"""
        
        if annual_vol < 20:
            response_text += "• **Stable Market** - Low volatility indicates predictable pricing"
        elif annual_vol < 50:
            response_text += "• **Moderate Volatility** - Normal market fluctuations"
        else:
            response_text += "• **High Volatility** - Significant price swings observed"
        
        if cv < 0.2:
            response_text += "\n• **Consistent Pricing** - Low coefficient of variation"
        else:
            response_text += "\n• **Variable Pricing** - High relative price variation"
        
        return {
            'text': response_text,
            'charts': [],
            'metrics': {
                'daily_volatility': daily_vol,
                'annualized_volatility': annual_vol,
                'price_range': price_range
            }
        }
    
    def _generate_trend_response(self, query_params: Dict, analysis_result: Dict, original_query: str) -> Dict:
        """Generate response for trend analysis"""
        iso = query_params.get('iso', '').upper()
        hub = query_params.get('hub', '')
        hub_text = f" {hub.upper()} hub" if hub else ""
        
        direction = analysis_result.get('trend_direction', 'unknown')
        strength = analysis_result.get('trend_strength', 'unknown')
        slope_per_hour = analysis_result.get('slope_per_hour', 0)
        r_squared = analysis_result.get('r_squared', 0)
        daily_change = analysis_result.get('price_change_per_day', 0)
        
        response_text = f"""
Price trend analysis for {iso}{hub_text}:

📊 **Trend Analysis:**
• Direction: **{direction.title()}** trend detected
• Strength: **{strength.title()}** correlation (R² = {r_squared:.3f})
• Rate of Change: ${slope_per_hour:.4f}/MWh per hour

📈 **Price Movement:**
• Daily Price Change: ${daily_change:.2f}/MWh per day
• Trend Reliability: {r_squared*100:.1f}%

**Interpretation:**
"""
        
        if direction == 'increasing':
            response_text += f"• Prices are trending **upward** at ${abs(daily_change):.2f}/MWh per day"
        else:
            response_text += f"• Prices are trending **downward** at ${abs(daily_change):.2f}/MWh per day"
        
        if strength == 'strong':
            response_text += "\n• **Strong trend** - High confidence in direction"
        elif strength == 'moderate':
            response_text += "\n• **Moderate trend** - Some directional bias observed"
        else:
            response_text += "\n• **Weak trend** - Limited directional consistency"
        
        return {
            'text': response_text,
            'charts': [],
            'metrics': {
                'trend_direction': direction,
                'daily_change': daily_change,
                'r_squared': r_squared
            }
        }
    
    def _generate_peak_analysis_response(self, query_params: Dict, analysis_result: Dict, original_query: str) -> Dict:
        """Generate response for peak hours analysis"""
        iso = query_params.get('iso', '').upper()
        hub = query_params.get('hub', '')
        hub_text = f" {hub.upper()} hub" if hub else ""
        
        peak_avg = analysis_result.get('peak_average_price', 0)
        off_peak_avg = analysis_result.get('off_peak_average_price', 0)
        premium = analysis_result.get('peak_premium', 0)
        premium_pct = analysis_result.get('peak_premium_percentage', 0)
        highest_hour = analysis_result.get('highest_hour', 0)
        lowest_hour = analysis_result.get('lowest_hour', 0)
        
        response_text = f"""
Peak hours analysis for {iso}{hub_text}:

⚡ **Peak vs Off-Peak Pricing:**
• Peak Hours Average: ${peak_avg:.2f}/MWh (7 AM - 10 PM)
• Off-Peak Average: ${off_peak_avg:.2f}/MWh (11 PM - 6 AM)
• Peak Premium: ${premium:.2f}/MWh ({premium_pct:.1f}% higher)

🕐 **Hourly Patterns:**
• Highest Price Hour: {highest_hour}:00 (Hour {highest_hour})
• Lowest Price Hour: {lowest_hour}:00 (Hour {lowest_hour})

**Market Insights:**
"""
        
        if premium_pct > 30:
            response_text += "• **High peak premium** - Significant demand-driven price differences"
        elif premium_pct > 15:
            response_text += "• **Moderate peak premium** - Typical demand patterns observed"
        else:
            response_text += "• **Low peak premium** - Relatively flat pricing throughout the day"
        
        if 14 <= highest_hour <= 18:
            response_text += "\n• **Afternoon peak** - Typical demand pattern during business hours"
        elif 18 <= highest_hour <= 22:
            response_text += "\n• **Evening peak** - High demand during evening hours"
        
        return {
            'text': response_text,
            'charts': [],
            'metrics': {
                'peak_premium': premium,
                'peak_premium_percentage': premium_pct,
                'highest_hour': highest_hour
            }
        }
    
    def _generate_statistics_response(self, query_params: Dict, analysis_result: Dict, original_query: str) -> Dict:
        """Generate response for basic statistics"""
        iso = query_params.get('iso', '').upper()
        hub = query_params.get('hub', '')
        hub_text = f" {hub.upper()} hub" if hub else ""
        
        mean = analysis_result.get('mean', 0)
        median = analysis_result.get('median', 0)
        std_dev = analysis_result.get('std_dev', 0)
        min_price = analysis_result.get('min', 0)
        max_price = analysis_result.get('max', 0)
        count = analysis_result.get('count', 0)
        
        response_text = f"""
Statistical summary for {iso}{hub_text} market prices:

📊 **Central Tendency:**
• Mean Price: ${mean:.2f}/MWh
• Median Price: ${median:.2f}/MWh
• Standard Deviation: ${std_dev:.2f}/MWh

📈 **Price Range:**
• Minimum: ${min_price:.2f}/MWh
• Maximum: ${max_price:.2f}/MWh
• Data Points: {count:,} observations

**Market Characteristics:**
"""
        
        if abs(mean - median) / mean < 0.1:
            response_text += "• **Symmetric distribution** - Mean and median are close"
        else:
            response_text += "• **Skewed distribution** - Mean and median differ significantly"
        
        cv = std_dev / mean if mean > 0 else 0
        if cv < 0.2:
            response_text += "\n• **Low variability** - Consistent pricing patterns"
        elif cv < 0.5:
            response_text += "\n• **Moderate variability** - Normal price fluctuations"
        else:
            response_text += "\n• **High variability** - Significant price volatility"
        
        return {
            'text': response_text,
            'charts': [],
            'metrics': {
                'mean': mean,
                'median': median,
                'std_dev': std_dev,
                'coefficient_of_variation': cv
            }
        }
    
    def _generate_forecast_response(self, query_params: Dict, analysis_result: Dict, original_query: str) -> Dict:
        """Generate response for forecast analysis"""
        iso = query_params.get('iso', '').upper()
        hub = query_params.get('hub', '')
        hub_text = f" {hub.upper()} hub" if hub else ""
        
        method = analysis_result.get('forecast_method', 'Unknown method')
        forecast_hours = analysis_result.get('forecast_hours', 0)
        forecasts = analysis_result.get('forecasted_prices', [])
        base_price = analysis_result.get('base_price', 0)
        trend = analysis_result.get('trend_per_hour', 0)
        
        response_text = f"""
Price forecast for {iso}{hub_text}:

🔮 **Forecast Details:**
• Method: {method}
• Forecast Horizon: {forecast_hours} hours
• Base Price: ${base_price:.2f}/MWh
• Trend: ${trend:.4f}/MWh per hour

📈 **Price Predictions:**
"""
        
        if len(forecasts) >= 24:
            response_text += f"• Next Hour: ${forecasts[0]:.2f}/MWh\n"
            response_text += f"• 6 Hours: ${forecasts[5]:.2f}/MWh\n"
            response_text += f"• 12 Hours: ${forecasts[11]:.2f}/MWh\n"
            response_text += f"• 24 Hours: ${forecasts[23]:.2f}/MWh"
        else:
            for i, price in enumerate(forecasts[:5]):
                response_text += f"• Hour {i+1}: ${price:.2f}/MWh\n"
        
        response_text += "\n\n**Forecast Confidence:**\n"
        if abs(trend) < 0.01:
            response_text += "• **Stable prices** expected - minimal trend detected"
        elif trend > 0:
            response_text += "• **Rising prices** expected - positive trend detected"
        else:
            response_text += "• **Declining prices** expected - negative trend detected"
        
        return {
            'text': response_text,
            'charts': [],
            'metrics': {
                'base_price': base_price,
                'trend_per_hour': trend,
                'forecast_hours': forecast_hours
            }
        }
    
    def _generate_comprehensive_response(self, query_params: Dict, analysis_result: Dict, original_query: str) -> Dict:
        """Generate comprehensive response for multiple analyses"""
        iso = query_params.get('iso', '').upper()
        hub = query_params.get('hub', '')
        hub_text = f" {hub.upper()} hub" if hub else ""
        
        response_text = f"Comprehensive market analysis for {iso}{hub_text}:\n\n"
        
        # Include key metrics from each analysis
        if 'statistics' in analysis_result:
            stats = analysis_result['statistics']
            response_text += f"📊 **Price Summary:** ${stats.get('mean', 0):.2f}/MWh average, "
            response_text += f"${stats.get('std_dev', 0):.2f}/MWh volatility\n\n"
        
        if 'volatility' in analysis_result:
            vol = analysis_result['volatility']
            response_text += f"📈 **Volatility:** {vol.get('annualized_volatility', 0):.1f}% annualized\n\n"
        
        if 'price_trend' in analysis_result:
            trend = analysis_result['price_trend']
            response_text += f"📊 **Trend:** {trend.get('trend_direction', 'Unknown').title()} "
            response_text += f"({trend.get('price_change_per_day', 0):+.2f}/MWh per day)\n\n"
        
        response_text += "This analysis provides a comprehensive view of market conditions and price dynamics."
        
        return {
            'text': response_text,
            'charts': [],
            'metrics': {}
        }
    
    # Placeholder methods for other analysis types
    def _generate_mae_response(self, query_params: Dict, analysis_result: Dict, original_query: str) -> Dict:
        return self._generate_rmse_response(query_params, analysis_result, original_query)
    
    def _generate_moving_average_response(self, query_params: Dict, analysis_result: Dict, original_query: str) -> Dict:
        return self._generate_statistics_response(query_params, analysis_result, original_query)
    
    def _generate_correlation_response(self, query_params: Dict, analysis_result: Dict, original_query: str) -> Dict:
        return self._generate_statistics_response(query_params, analysis_result, original_query)
