"""
Mood Engine - Advanced mood simulation and behavior modification for roleplay personas
"""

import logging
import random
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from services.roleplay_service import MoodType

logger = logging.getLogger(__name__)

@dataclass
class MoodBehavior:
    """Defines specific behavioral patterns for a mood"""
    mood_type: MoodType
    engagement_level: float  # 0.0 to 1.0
    challenge_intensity: float  # 0.0 to 1.0
    question_frequency: float  # 0.0 to 1.0
    interruption_tendency: float  # 0.0 to 1.0
    detail_seeking: float  # 0.0 to 1.0
    skepticism_level: float  # 0.0 to 1.0

@dataclass
class ResponsePattern:
    """Defines response patterns for different moods"""
    opening_phrases: List[str]
    questioning_styles: List[str]
    challenge_phrases: List[str]
    agreement_phrases: List[str]
    disagreement_phrases: List[str]
    transition_phrases: List[str]
    closing_phrases: List[str]

class MoodEngine:
    """Advanced mood simulation engine for realistic persona behavior"""
    
    def __init__(self, ai_analyzer=None):
        self.ai_analyzer = ai_analyzer
        self.mood_behaviors = self._initialize_mood_behaviors()
        self.response_patterns = self._initialize_response_patterns()
        self.mood_transitions = self._initialize_mood_transitions()
        
    def _initialize_mood_behaviors(self) -> Dict[MoodType, MoodBehavior]:
        """Initialize behavioral parameters for each mood type"""
        return {
            MoodType.CURIOUS: MoodBehavior(
                mood_type=MoodType.CURIOUS,
                engagement_level=0.9,
                challenge_intensity=0.4,
                question_frequency=0.8,
                interruption_tendency=0.3,
                detail_seeking=0.9,
                skepticism_level=0.2
            ),
            MoodType.SKEPTICAL: MoodBehavior(
                mood_type=MoodType.SKEPTICAL,
                engagement_level=0.8,
                challenge_intensity=0.9,
                question_frequency=0.7,
                interruption_tendency=0.6,
                detail_seeking=0.7,
                skepticism_level=0.9
            ),
            MoodType.INDIFFERENT: MoodBehavior(
                mood_type=MoodType.INDIFFERENT,
                engagement_level=0.5,
                challenge_intensity=0.3,
                question_frequency=0.4,
                interruption_tendency=0.2,
                detail_seeking=0.4,
                skepticism_level=0.4
            ),
            MoodType.PASSIVE: MoodBehavior(
                mood_type=MoodType.PASSIVE,
                engagement_level=0.3,
                challenge_intensity=0.1,
                question_frequency=0.2,
                interruption_tendency=0.1,
                detail_seeking=0.2,
                skepticism_level=0.2
            ),
            MoodType.NOT_INTERESTED: MoodBehavior(
                mood_type=MoodType.NOT_INTERESTED,
                engagement_level=0.2,
                challenge_intensity=0.6,
                question_frequency=0.3,
                interruption_tendency=0.7,
                detail_seeking=0.1,
                skepticism_level=0.7
            )
        }
    
    def _initialize_response_patterns(self) -> Dict[MoodType, ResponsePattern]:
        """Initialize response patterns for each mood type"""
        return {
            MoodType.CURIOUS: ResponsePattern(
                opening_phrases=[
                    "That's interesting, tell me more about",
                    "I'd like to understand better how",
                    "Can you elaborate on",
                    "Help me understand the details of",
                    "I'm curious about"
                ],
                questioning_styles=[
                    "What happens if we consider",
                    "How does this work when",
                    "Can you walk me through",
                    "What are the implications of",
                    "How would this affect"
                ],
                challenge_phrases=[
                    "I wonder if we've considered",
                    "What about the scenario where",
                    "Have you thought about",
                    "How do we address",
                    "What if we encounter"
                ],
                agreement_phrases=[
                    "That makes sense, and",
                    "I can see how that would work, especially",
                    "That's a good point about",
                    "I appreciate that perspective on"
                ],
                disagreement_phrases=[
                    "I'm not sure I follow the logic on",
                    "I have some concerns about",
                    "I'm wondering if there's another way to",
                    "Could we explore alternatives to"
                ],
                transition_phrases=[
                    "Building on that idea",
                    "That leads me to another question",
                    "Speaking of which",
                    "On a related note"
                ],
                closing_phrases=[
                    "This has been very enlightening",
                    "I have a much better understanding now",
                    "Thank you for the detailed explanation",
                    "This gives me a lot to think about"
                ]
            ),
            MoodType.SKEPTICAL: ResponsePattern(
                opening_phrases=[
                    "I have some concerns about",
                    "I'm not convinced that",
                    "How can you prove",
                    "I question whether",
                    "I'm skeptical about"
                ],
                questioning_styles=[
                    "What evidence do you have that",
                    "How do you know",
                    "What guarantees can you provide that",
                    "What happens when this fails",
                    "How do you address the risk of"
                ],
                challenge_phrases=[
                    "That sounds too good to be true",
                    "I've heard similar promises before",
                    "What about all the cases where",
                    "How is this different from",
                    "I'm concerned about"
                ],
                agreement_phrases=[
                    "I suppose that could work, but",
                    "That's reasonable, assuming",
                    "I can accept that, provided",
                    "Fair enough, though I still worry about"
                ],
                disagreement_phrases=[
                    "I completely disagree with",
                    "That's not realistic because",
                    "I don't buy the argument that",
                    "That's a flawed assumption about"
                ],
                transition_phrases=[
                    "Another concern I have is",
                    "That raises another red flag",
                    "Speaking of problems",
                    "This reminds me of another issue"
                ],
                closing_phrases=[
                    "I remain unconvinced",
                    "You haven't addressed my main concerns",
                    "I need to see more evidence",
                    "I'll need to think about this more"
                ]
            ),
            MoodType.INDIFFERENT: ResponsePattern(
                opening_phrases=[
                    "Okay, so",
                    "I see",
                    "Right",
                    "Understood",
                    "Fine"
                ],
                questioning_styles=[
                    "What's the bottom line on",
                    "How much does this cost",
                    "When would this be implemented",
                    "What are the key facts about",
                    "What's the timeline for"
                ],
                challenge_phrases=[
                    "Is this really necessary",
                    "What's the point of",
                    "Why should we care about",
                    "How is this relevant to",
                    "What's the benefit of"
                ],
                agreement_phrases=[
                    "Sure, whatever",
                    "That's fine",
                    "Okay, I guess",
                    "If you say so"
                ],
                disagreement_phrases=[
                    "I don't think that matters",
                    "That's not really important",
                    "I'm not sure why we're discussing",
                    "That seems irrelevant"
                ],
                transition_phrases=[
                    "Moving on",
                    "What else",
                    "Next topic",
                    "Anything else"
                ],
                closing_phrases=[
                    "Alright, we're done here",
                    "I think I have what I need",
                    "Let's wrap this up",
                    "Is that everything"
                ]
            ),
            MoodType.PASSIVE: ResponsePattern(
                opening_phrases=[
                    "Mm-hmm",
                    "I see",
                    "Okay",
                    "Right",
                    "Yes"
                ],
                questioning_styles=[
                    "What else",
                    "And then",
                    "What about",
                    "Anything else",
                    "What happens next"
                ],
                challenge_phrases=[
                    "I'm not sure about",
                    "Maybe we should consider",
                    "I wonder if",
                    "Perhaps",
                    "I suppose"
                ],
                agreement_phrases=[
                    "That sounds good",
                    "Okay, sure",
                    "I agree",
                    "That works for me"
                ],
                disagreement_phrases=[
                    "I'm not sure about that",
                    "Maybe not",
                    "I don't know",
                    "I'm not convinced"
                ],
                transition_phrases=[
                    "So",
                    "Well",
                    "Then",
                    "I guess"
                ],
                closing_phrases=[
                    "Thank you",
                    "That was helpful",
                    "I appreciate your time",
                    "Good to know"
                ]
            ),
            MoodType.NOT_INTERESTED: ResponsePattern(
                opening_phrases=[
                    "Look, I don't have much time for",
                    "I'm not really interested in",
                    "Can we speed this up",
                    "I don't see the point of",
                    "Why are we discussing"
                ],
                questioning_styles=[
                    "How long is this going to take",
                    "What's the bottom line",
                    "Can you just tell me",
                    "Do I really need to know about",
                    "Is this going somewhere"
                ],
                challenge_phrases=[
                    "This is a waste of time",
                    "I don't see how this helps",
                    "This isn't relevant to",
                    "Why should I care about",
                    "This doesn't make sense"
                ],
                agreement_phrases=[
                    "Fine, whatever gets us done faster",
                    "Sure, if it means we can finish",
                    "Okay, let's just move on",
                    "I suppose, but let's hurry"
                ],
                disagreement_phrases=[
                    "That's completely wrong",
                    "I disagree entirely",
                    "That makes no sense",
                    "That's not how it works"
                ],
                transition_phrases=[
                    "Can we just",
                    "Let's get to the point",
                    "I need to wrap this up",
                    "Time is running out"
                ],
                closing_phrases=[
                    "I need to go",
                    "We're done here",
                    "I have other priorities",
                    "This meeting is over"
                ]
            )
        }
    
    def _initialize_mood_transitions(self) -> Dict[MoodType, Dict[str, float]]:
        """Initialize mood transition probabilities based on interaction quality"""
        return {
            MoodType.CURIOUS: {
                'positive_response': {'curious': 0.7, 'skeptical': 0.1, 'indifferent': 0.1, 'passive': 0.1},
                'negative_response': {'skeptical': 0.4, 'indifferent': 0.3, 'curious': 0.2, 'not_interested': 0.1}
            },
            MoodType.SKEPTICAL: {
                'positive_response': {'curious': 0.3, 'skeptical': 0.4, 'indifferent': 0.2, 'passive': 0.1},
                'negative_response': {'skeptical': 0.5, 'not_interested': 0.3, 'indifferent': 0.2}
            },
            MoodType.INDIFFERENT: {
                'positive_response': {'curious': 0.3, 'indifferent': 0.4, 'passive': 0.2, 'skeptical': 0.1},
                'negative_response': {'not_interested': 0.4, 'indifferent': 0.3, 'skeptical': 0.2, 'passive': 0.1}
            },
            MoodType.PASSIVE: {
                'positive_response': {'curious': 0.2, 'passive': 0.5, 'indifferent': 0.2, 'skeptical': 0.1},
                'negative_response': {'not_interested': 0.3, 'passive': 0.4, 'indifferent': 0.3}
            },
            MoodType.NOT_INTERESTED: {
                'positive_response': {'indifferent': 0.4, 'skeptical': 0.3, 'not_interested': 0.2, 'passive': 0.1},
                'negative_response': {'not_interested': 0.8, 'skeptical': 0.2}
            }
        }
    
    def generate_mood_response(self, mood_type: MoodType, context: Dict[str, Any], 
                             user_response_quality: float = 0.5) -> Dict[str, Any]:
        """Generate a mood-appropriate response based on context and user performance"""
        try:
            mood_behavior = self.mood_behaviors[mood_type]
            response_pattern = self.response_patterns[mood_type]
            
            # Determine response type based on mood and context
            response_type = self._determine_response_type(mood_behavior, context, user_response_quality)
            
            # Generate appropriate phrases
            response_components = self._select_response_components(response_pattern, response_type)
            
            # Apply mood-specific modifications
            modified_response = self._apply_mood_modifications(
                response_components, mood_behavior, context
            )
            
            # Determine if mood should transition
            new_mood = self._check_mood_transition(mood_type, user_response_quality)
            
            return {
                'response_type': response_type,
                'response_components': modified_response,
                'engagement_level': mood_behavior.engagement_level,
                'challenge_intensity': mood_behavior.challenge_intensity,
                'mood_transition': new_mood if new_mood != mood_type else None,
                'behavioral_notes': self._generate_behavioral_notes(mood_behavior, response_type)
            }
            
        except Exception as e:
            logger.error(f"Error generating mood response: {e}")
            return {'error': f'Failed to generate mood response: {e}'}
    
    def _determine_response_type(self, mood_behavior: MoodBehavior, context: Dict[str, Any], 
                               user_quality: float) -> str:
        """Determine the type of response based on mood and context"""
        # Base response type on mood characteristics
        if mood_behavior.challenge_intensity > 0.7:
            if user_quality < 0.4:
                return 'strong_challenge'
            elif user_quality < 0.7:
                return 'moderate_challenge'
            else:
                return 'skeptical_acceptance'
        elif mood_behavior.engagement_level > 0.7:
            if user_quality > 0.6:
                return 'enthusiastic_follow_up'
            else:
                return 'curious_probing'
        elif mood_behavior.engagement_level < 0.4:
            if user_quality < 0.5:
                return 'disengaged_dismissal'
            else:
                return 'minimal_acknowledgment'
        else:
            return 'neutral_response'
    
    def _select_response_components(self, pattern: ResponsePattern, response_type: str) -> Dict[str, str]:
        """Select appropriate response components based on type"""
        components = {}
        
        if response_type in ['strong_challenge', 'moderate_challenge']:
            components['opening'] = random.choice(pattern.challenge_phrases)
            components['questioning'] = random.choice(pattern.questioning_styles)
            components['tone'] = 'challenging'
        elif response_type in ['enthusiastic_follow_up', 'curious_probing']:
            components['opening'] = random.choice(pattern.opening_phrases)
            components['questioning'] = random.choice(pattern.questioning_styles)
            components['tone'] = 'engaged'
        elif response_type == 'disengaged_dismissal':
            components['opening'] = random.choice(pattern.disagreement_phrases)
            components['transition'] = random.choice(pattern.transition_phrases)
            components['tone'] = 'dismissive'
        elif response_type == 'skeptical_acceptance':
            components['opening'] = random.choice(pattern.agreement_phrases)
            components['questioning'] = random.choice(pattern.challenge_phrases)
            components['tone'] = 'cautious'
        else:  # neutral_response, minimal_acknowledgment
            components['opening'] = random.choice(pattern.opening_phrases)
            components['tone'] = 'neutral'
        
        return components
    
    def _apply_mood_modifications(self, components: Dict[str, str], 
                                mood_behavior: MoodBehavior, context: Dict[str, Any]) -> Dict[str, str]:
        """Apply mood-specific modifications to response components"""
        modified = components.copy()
        
        # Adjust based on interruption tendency
        if mood_behavior.interruption_tendency > 0.6:
            modified['interruption_marker'] = "[interrupting]"
        
        # Adjust based on detail seeking
        if mood_behavior.detail_seeking > 0.7:
            modified['detail_request'] = "I need more specifics on"
        elif mood_behavior.detail_seeking < 0.3:
            modified['brevity_request'] = "Just give me the highlights"
        
        # Add skepticism markers
        if mood_behavior.skepticism_level > 0.7:
            modified['skepticism_marker'] = "[with visible doubt]"
        
        # Add engagement indicators
        if mood_behavior.engagement_level > 0.8:
            modified['engagement_marker'] = "[leaning forward, taking notes]"
        elif mood_behavior.engagement_level < 0.3:
            modified['disengagement_marker'] = "[checking phone, looking at watch]"
        
        return modified
    
    def _check_mood_transition(self, current_mood: MoodType, user_quality: float) -> MoodType:
        """Check if mood should transition based on user response quality"""
        transitions = self.mood_transitions.get(current_mood, {})
        
        # Determine if response was positive or negative
        response_category = 'positive_response' if user_quality > 0.6 else 'negative_response'
        transition_probs = transitions.get(response_category, {})
        
        # Simple probability-based transition (in real implementation, could be more sophisticated)
        if transition_probs and random.random() < 0.2:  # 20% chance of mood transition
            # Select new mood based on probabilities
            mood_options = list(transition_probs.keys())
            probabilities = list(transition_probs.values())
            
            # Weighted random selection
            total = sum(probabilities)
            r = random.uniform(0, total)
            cumulative = 0
            
            for mood, prob in zip(mood_options, probabilities):
                cumulative += prob
                if r <= cumulative:
                    try:
                        return MoodType(mood)
                    except ValueError:
                        break
        
        return current_mood
    
    def _generate_behavioral_notes(self, mood_behavior: MoodBehavior, response_type: str) -> List[str]:
        """Generate behavioral notes for the response"""
        notes = []
        
        if mood_behavior.engagement_level > 0.8:
            notes.append("Highly engaged and attentive")
        elif mood_behavior.engagement_level < 0.3:
            notes.append("Shows signs of disengagement")
        
        if mood_behavior.challenge_intensity > 0.7:
            notes.append("Challenging and probing responses")
        
        if mood_behavior.interruption_tendency > 0.6:
            notes.append("May interrupt or speak over presenter")
        
        if mood_behavior.detail_seeking > 0.7:
            notes.append("Seeks detailed explanations and examples")
        elif mood_behavior.detail_seeking < 0.3:
            notes.append("Prefers high-level summaries")
        
        return notes
    
    def get_mood_coaching_tips(self, mood_type: MoodType) -> List[str]:
        """Get coaching tips for handling specific mood types"""
        tips = {
            MoodType.CURIOUS: [
                "Prepare detailed examples and case studies",
                "Be ready for follow-up questions and deep dives",
                "Encourage their curiosity while staying on track",
                "Provide additional resources for further exploration"
            ],
            MoodType.SKEPTICAL: [
                "Bring concrete evidence and proof points",
                "Acknowledge their concerns directly",
                "Use third-party validation and references",
                "Be prepared to address worst-case scenarios"
            ],
            MoodType.INDIFFERENT: [
                "Lead with compelling value propositions",
                "Focus on bottom-line impact and benefits",
                "Keep presentations concise and to the point",
                "Use engaging visuals and interactive elements"
            ],
            MoodType.PASSIVE: [
                "Ask direct questions to encourage participation",
                "Use interactive exercises and discussions",
                "Check for understanding frequently",
                "Create safe spaces for them to contribute"
            ],
            MoodType.NOT_INTERESTED: [
                "Start with the most compelling value proposition",
                "Respect their time constraints",
                "Focus on immediate, tangible benefits",
                "Be prepared to reschedule if necessary"
            ]
        }
        
        return tips.get(mood_type, [])
