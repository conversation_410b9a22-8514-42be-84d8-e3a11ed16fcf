#!/usr/bin/env python3
"""
Test AI integration with Gemini 2.5 Flash
"""

import sys
import os
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.ai_nlp_service import AIMarketAnalyzer

def test_ai_nlp():
    """Test AI-powered natural language processing"""
    print("🤖 TESTING AI-POWERED NATURAL LANGUAGE PROCESSING")
    print("=" * 60)
    
    ai_analyzer = AIMarketAnalyzer()
    
    if not ai_analyzer.model:
        print("❌ Gemini API not available. Check your API key.")
        return False
    
    print(f"✅ Gemini 2.5 Flash model initialized successfully")
    
    # Test queries
    test_queries = [
        "ERCOT LMP statistics this week",
        "What is the RMSE for NDDAM prices in PJM north hub?",
        "Show me volatility for CAISO real-time prices",
        "Calculate MAPE for MISO bal day prices last month",
        "RT price trends for NYISO this week"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: '{query}'")
        print("-" * 40)
        
        try:
            # Parse the query using AI
            result = ai_analyzer.parse_natural_language_query(query)
            
            if result and result.get('confidence', 0) > 0.5:
                print("✅ AI parsing successful:")
                print(f"   ISO: {result.get('iso', 'N/A')}")
                print(f"   Data Type: {result.get('data_type', 'N/A')}")
                print(f"   Hub/Node: {result.get('hub_or_node', 'N/A')}")
                print(f"   Analysis: {result.get('analysis_type', 'N/A')}")
                print(f"   Time Period: {result.get('period_description', 'N/A')}")
                print(f"   Confidence: {result.get('confidence', 0):.1%}")
                
                if result.get('specific_calculation'):
                    print(f"   Specific Calc: {result.get('specific_calculation')}")
                    
            else:
                print("❌ AI parsing failed or low confidence")
                if result:
                    print(f"   Confidence: {result.get('confidence', 0):.1%}")
                    
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    return True

def test_ai_sensor_matching():
    """Test AI sensor matching capabilities"""
    print("\n\n🎯 TESTING AI SENSOR MATCHING")
    print("=" * 60)
    
    ai_analyzer = AIMarketAnalyzer()
    
    if not ai_analyzer.model:
        print("❌ Gemini API not available")
        return False
    
    # Mock sensor data for testing
    mock_sensors = [
        {
            'sensor_id': 'ERCOT_LMP_NORTH_HUB_RT',
            'description': 'ERCOT North Hub Real-Time Locational Marginal Price',
            'report_region': 'ERCOT',
            'report_name': 'Real-Time LMP Report'
        },
        {
            'sensor_id': 'ERCOT_NDDAM_SOUTH_HUB',
            'description': 'ERCOT South Hub Next Day Day-Ahead Market Price',
            'report_region': 'ERCOT',
            'report_name': 'Day-Ahead Market Report'
        },
        {
            'sensor_id': 'PJM_LMP_WEST_HUB_DA',
            'description': 'PJM Western Hub Day-Ahead Locational Marginal Price',
            'report_region': 'PJM',
            'report_name': 'Day-Ahead LMP Report'
        },
        {
            'sensor_id': 'CAISO_RT_LOAD_TOTAL',
            'description': 'CAISO Total System Load Real-Time',
            'report_region': 'CAISO',
            'report_name': 'Real-Time Load Report'
        }
    ]
    
    # Test query parameters
    test_params = [
        {
            'iso': 'ercot',
            'data_type': 'lmp',
            'hub_or_node': 'north',
            'analysis_type': 'statistics'
        },
        {
            'iso': 'pjm',
            'data_type': 'da',
            'hub_or_node': 'west',
            'analysis_type': 'volatility'
        }
    ]
    
    for i, params in enumerate(test_params, 1):
        print(f"\n🔍 Test {i}: Finding sensors for {params}")
        print("-" * 40)
        
        try:
            ranked_sensors = ai_analyzer.find_best_sensors(params, mock_sensors)
            
            if ranked_sensors:
                print(f"✅ Found {len(ranked_sensors)} matching sensors:")
                for j, sensor in enumerate(ranked_sensors[:3], 1):
                    print(f"   {j}. {sensor['sensor_id']}")
                    print(f"      {sensor['description'][:60]}...")
            else:
                print("❌ No matching sensors found")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    return True

def test_ai_dynamic_analysis():
    """Test AI dynamic analysis generation"""
    print("\n\n📊 TESTING AI DYNAMIC ANALYSIS")
    print("=" * 60)
    
    ai_analyzer = AIMarketAnalyzer()
    
    if not ai_analyzer.model:
        print("❌ Gemini API not available")
        return False
    
    # Create mock data
    import pandas as pd
    import numpy as np
    
    # Generate sample price data
    dates = pd.date_range('2025-09-10', '2025-09-16', freq='h')
    prices = 50 + 10 * np.sin(np.arange(len(dates)) * 2 * np.pi / 24) + np.random.normal(0, 5, len(dates))
    
    mock_data = pd.DataFrame({
        'timestamp': dates,
        'price': prices
    })
    
    # Test query parameters
    test_params = {
        'iso': 'ercot',
        'data_type': 'lmp',
        'analysis_type': 'rmse',
        'specific_calculation': 'Calculate RMSE using 24-hour moving average as baseline'
    }
    
    print(f"🔍 Testing dynamic analysis for: {test_params['specific_calculation']}")
    print("-" * 40)
    
    try:
        analysis_result = ai_analyzer.generate_dynamic_analysis(test_params, mock_data)
        
        if 'error' not in analysis_result:
            print("✅ AI dynamic analysis successful:")
            for key, value in analysis_result.items():
                if isinstance(value, (int, float)):
                    if abs(value) < 1:
                        print(f"   {key}: {value:.4f}")
                    else:
                        print(f"   {key}: {value:.2f}")
                else:
                    print(f"   {key}: {value}")
        else:
            print(f"❌ AI analysis failed: {analysis_result['error']}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return True

def main():
    """Run all AI integration tests"""
    print("🚀 AI INTEGRATION TEST SUITE")
    print(f"Test started at: {datetime.now()}")
    print(f"Gemini API Key: {os.getenv('GEMINI_API_KEY', 'Not found')[:20]}...")
    
    try:
        # Test 1: Natural Language Processing
        nlp_success = test_ai_nlp()
        
        # Test 2: Sensor Matching
        sensor_success = test_ai_sensor_matching()
        
        # Test 3: Dynamic Analysis
        analysis_success = test_ai_dynamic_analysis()
        
        print("\n\n🎉 AI INTEGRATION TESTS COMPLETED!")
        print("=" * 60)
        
        if nlp_success and sensor_success and analysis_success:
            print("✅ ALL TESTS PASSED - AI integration is working perfectly!")
            print("\nYour chatbot now has:")
            print("🧠 Intelligent natural language understanding")
            print("🎯 Smart sensor discovery and matching")
            print("📊 Dynamic analysis generation")
            print("🤖 Powered by Gemini 2.5 Flash")
        else:
            print("⚠️ Some tests had issues - check the logs above")
        
        print(f"\nNext steps:")
        print("1. Start the web application: python app.py")
        print("2. Test with queries like:")
        print("   • 'ERCOT NDDAM price volatility this week'")
        print("   • 'Calculate RMSE for PJM LMP north hub'")
        print("   • 'Show me RT price trends for CAISO'")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
