#!/usr/bin/env python3
"""
Test script to verify real Genscape API integration
"""

import sys
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.api_service import MarketDataAPI

def test_metadata_api():
    """Test the metadata API with real credentials"""
    print("=" * 60)
    print("TESTING REAL GENSCAPE METADATA API")
    print("=" * 60)
    
    api = MarketDataAPI()
    
    print(f"API Key: {api.api_key[:10]}...")
    print(f"Metadata URL: {api.metadata_base_url}")
    
    # Test 1: Search for ERCOT sensors
    print("\n1. Searching for ERCOT sensors...")
    ercot_sensors = api.search_sensors(['ercot'], limit=10)
    
    if ercot_sensors:
        print(f"✅ Found {len(ercot_sensors)} ERCOT sensors")
        for i, sensor in enumerate(ercot_sensors[:3]):
            print(f"   {i+1}. ID: {sensor.get('sensor_id')} - {sensor.get('description')[:80]}...")
    else:
        print("❌ No ERCOT sensors found")
    
    # Test 2: Search for LMP sensors
    print("\n2. Searching for LMP sensors...")
    lmp_sensors = api.search_sensors(['lmp', 'locational marginal price'], limit=10)
    
    if lmp_sensors:
        print(f"✅ Found {len(lmp_sensors)} LMP sensors")
        for i, sensor in enumerate(lmp_sensors[:3]):
            print(f"   {i+1}. ID: {sensor.get('sensor_id')} - {sensor.get('description')[:80]}...")
    else:
        print("❌ No LMP sensors found")
    
    # Test 3: Search for ERCOT LMP specifically
    print("\n3. Searching for ERCOT LMP sensors...")
    ercot_lmp_sensors = api.search_sensors(['ercot', 'lmp'], limit=5)
    
    if ercot_lmp_sensors:
        print(f"✅ Found {len(ercot_lmp_sensors)} ERCOT LMP sensors")
        for i, sensor in enumerate(ercot_lmp_sensors):
            print(f"   {i+1}. ID: {sensor.get('sensor_id')} - {sensor.get('description')}")
        
        # Test sensor finding
        print("\n4. Testing sensor finding for 'ERCOT LMP'...")
        best_sensor = api.find_sensor_for_query('ercot', 'lmp')
        if best_sensor:
            print(f"✅ Best sensor found: {best_sensor}")
        else:
            print("❌ No best sensor found")
            
        return ercot_lmp_sensors[0]['sensor_id'] if ercot_lmp_sensors else None
    else:
        print("❌ No ERCOT LMP sensors found")
        return None

def test_market_data_api(sensor_id):
    """Test the market data API with a real sensor ID"""
    print("\n" + "=" * 60)
    print("TESTING REAL GENSCAPE MARKET DATA API")
    print("=" * 60)
    
    if not sensor_id:
        print("❌ No sensor ID available for testing")
        return
    
    api = MarketDataAPI()
    
    print(f"Market Data URL: {api.market_price_base_url}")
    print(f"Testing with sensor ID: {sensor_id}")
    
    # Test with recent dates
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
    
    print(f"Date range: {start_date} to {end_date}")
    
    # Test market data fetch
    print("\n1. Fetching market data...")
    data = api.fetch_market_data(sensor_id, start_date, end_date, 'hourly')
    
    if data is not None and not data.empty:
        print(f"✅ Successfully fetched {len(data)} data points")
        print(f"   Date range: {data['timestamp'].min()} to {data['timestamp'].max()}")
        print(f"   Price range: ${data['price'].min():.2f} - ${data['price'].max():.2f}")
        print(f"   Sample data:")
        print(data.head(3).to_string(index=False))
        return True
    else:
        print("❌ No market data returned")
        return False

def test_end_to_end():
    """Test complete end-to-end functionality with real APIs"""
    print("\n" + "=" * 60)
    print("TESTING END-TO-END WITH REAL APIS")
    print("=" * 60)
    
    from services.nlp_service import NLPProcessor
    from services.analysis_service import DataAnalyzer
    from services.response_generator import ResponseGenerator
    
    # Initialize services
    nlp = NLPProcessor()
    api = MarketDataAPI()
    analyzer = DataAnalyzer()
    response_gen = ResponseGenerator()
    
    # Test query
    query = "ERCOT LMP statistics this week"
    print(f"Testing query: '{query}'")
    
    # Parse query
    print("\n1. Parsing query...")
    query_params = nlp.parse_query(query)
    if query_params:
        print(f"✅ Parsed: ISO={query_params['iso']}, DataType={query_params['data_type']}, Analysis={query_params['analysis_type']}")
    else:
        print("❌ Failed to parse query")
        return
    
    # Fetch data
    print("\n2. Fetching data...")
    data = api.fetch_data(
        iso=query_params['iso'],
        hub=query_params.get('hub'),
        data_type=query_params['data_type'],
        start_date=query_params['start_date'],
        end_date=query_params['end_date']
    )
    
    if data is not None and not data.empty:
        print(f"✅ Data fetched: {len(data)} points from {data['timestamp'].min().date()} to {data['timestamp'].max().date()}")
    else:
        print("❌ No data fetched")
        return
    
    # Analyze data
    print("\n3. Analyzing data...")
    analysis_result = analyzer.analyze(data, query_params['analysis_type'])
    if 'error' not in analysis_result:
        print("✅ Analysis completed successfully")
        print(f"   Mean price: ${analysis_result.get('mean', 0):.2f}/MWh")
        print(f"   Data points: {analysis_result.get('count', 0)}")
    else:
        print(f"❌ Analysis failed: {analysis_result['error']}")
        return
    
    # Generate response
    print("\n4. Generating response...")
    response = response_gen.generate_response(query_params, analysis_result, query)
    print("✅ Response generated")
    
    # Show response preview
    response_lines = response['text'].split('\n')
    print(f"\n📊 Response preview:")
    print(response_lines[0])
    if len(response_lines) > 1:
        print(response_lines[1])
    print("...")

def main():
    """Run all real API tests"""
    print("Market Price Analysis Chatbot - Real API Test Suite")
    print(f"Test started at: {datetime.now()}")
    
    try:
        # Test metadata API and get a sensor ID
        sensor_id = test_metadata_api()
        
        # Test market data API if we have a sensor ID
        if sensor_id:
            success = test_market_data_api(sensor_id)
            if success:
                # Test complete end-to-end flow
                test_end_to_end()
        
        print("\n" + "=" * 60)
        print("REAL API TESTS COMPLETED! 🎉")
        print("=" * 60)
        print("\nThe chatbot is now connected to real Genscape APIs.")
        print("You can use the web interface at http://127.0.0.1:5000")
        print("\nTry queries like:")
        print("• 'ERCOT LMP statistics this week'")
        print("• 'NDDAM price volatility for ERCOT'")
        print("• 'What is the RMSE for PJM LMP?'")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
