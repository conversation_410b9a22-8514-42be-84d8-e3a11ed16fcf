#!/usr/bin/env python3
"""
Test different authentication methods for Genscape API
"""

import requests
import os
from dotenv import load_dotenv

load_dotenv()

API_KEY = os.getenv('GENSCAPE_API_KEY')
BASE_URL = "https://api.genscape.com/marketintelligence/na"

def test_auth_methods():
    """Test different authentication methods"""
    print("Testing different authentication methods for Genscape API")
    print(f"API Key: {API_KEY[:10]}...")
    print(f"Base URL: {BASE_URL}")
    
    # Test endpoint
    endpoint = f"{BASE_URL}/v1/searchreportmetadata"
    
    # Method 1: Bearer token in Authorization header
    print("\n1. Testing Bearer token in Authorization header...")
    headers1 = {
        'Authorization': f'Bearer {API_KEY}',
        'Accept': 'application/json'
    }
    
    try:
        response = requests.get(endpoint, headers=headers1, params={'limit': 5}, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ SUCCESS with Bearer token!")
            return "bearer"
        else:
            print(f"   Response: {response.text[:200]}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Method 2: API key in X-API-Key header
    print("\n2. Testing X-API-Key header...")
    headers2 = {
        'X-API-Key': API_KEY,
        'Accept': 'application/json'
    }
    
    try:
        response = requests.get(endpoint, headers=headers2, params={'limit': 5}, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ SUCCESS with X-API-Key!")
            return "x-api-key"
        else:
            print(f"   Response: {response.text[:200]}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Method 3: API key as query parameter
    print("\n3. Testing API key as query parameter...")
    headers3 = {
        'Accept': 'application/json'
    }
    params3 = {
        'apikey': API_KEY,
        'limit': 5
    }
    
    try:
        response = requests.get(endpoint, headers=headers3, params=params3, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ SUCCESS with query parameter!")
            return "query_param"
        else:
            print(f"   Response: {response.text[:200]}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Method 4: API key in custom header
    print("\n4. Testing custom apikey header...")
    headers4 = {
        'apikey': API_KEY,
        'Accept': 'application/json'
    }
    
    try:
        response = requests.get(endpoint, headers=headers4, params={'limit': 5}, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ SUCCESS with custom header!")
            return "custom_header"
        else:
            print(f"   Response: {response.text[:200]}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Method 5: Try without authentication to see what happens
    print("\n5. Testing without authentication...")
    headers5 = {
        'Accept': 'application/json'
    }
    
    try:
        response = requests.get(endpoint, headers=headers5, params={'limit': 5}, timeout=10)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:200]}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    return None

def test_different_endpoints():
    """Test different API endpoints to see which ones work"""
    print("\n" + "="*60)
    print("TESTING DIFFERENT ENDPOINTS")
    print("="*60)
    
    endpoints = [
        "/v1/searchreportmetadata",
        "/v1/getepcalcsiddata",
        "/searchreportmetadata",
        "/getepcalcsiddata"
    ]
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'X-API-Key': API_KEY,
        'apikey': API_KEY,
        'Accept': 'application/json'
    }
    
    for endpoint in endpoints:
        url = f"{BASE_URL}{endpoint}"
        print(f"\nTesting: {url}")
        
        try:
            response = requests.get(url, headers=headers, params={'limit': 1}, timeout=10)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ SUCCESS!")
                data = response.json()
                print(f"   Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            else:
                print(f"   Response: {response.text[:150]}")
        except Exception as e:
            print(f"   Error: {str(e)}")

if __name__ == "__main__":
    if not API_KEY:
        print("❌ No API key found in environment variables")
        exit(1)
    
    # Test authentication methods
    working_method = test_auth_methods()
    
    if working_method:
        print(f"\n🎉 Found working authentication method: {working_method}")
    else:
        print("\n❌ No working authentication method found")
        print("Let's try different endpoints...")
        test_different_endpoints()
    
    print("\n" + "="*60)
    print("API AUTHENTICATION TEST COMPLETED")
    print("="*60)
