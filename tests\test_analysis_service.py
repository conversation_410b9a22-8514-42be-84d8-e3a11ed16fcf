import unittest
import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.analysis_service import DataAnalyzer

class TestAnalysisService(unittest.TestCase):
    """Test cases for the data analysis service"""
    
    def setUp(self):
        self.analyzer = DataAnalyzer()
        
        # Create sample data for testing
        dates = pd.date_range(start='2024-01-01', end='2024-01-07', freq='H')
        np.random.seed(42)  # For reproducible results
        
        # Generate realistic price data
        base_prices = []
        for date in dates:
            hour = date.hour
            # Peak hours have higher prices
            if 7 <= hour <= 22:
                base_price = 60 + np.random.normal(0, 10)
            else:
                base_price = 40 + np.random.normal(0, 5)
            base_prices.append(max(base_price, 10))  # Minimum price floor
        
        self.sample_data = pd.DataFrame({
            'timestamp': dates,
            'price': base_prices,
            'iso': 'ERCOT',
            'hub': 'NORTH_HUB'
        })
    
    def test_calculate_rmse(self):
        """Test RMSE calculation"""
        result = self.analyzer.analyze(self.sample_data, 'rmse', {'window': 24})
        
        self.assertIn('rmse', result)
        self.assertIn('mae', result)
        self.assertIn('mape', result)
        self.assertIsInstance(result['rmse'], (int, float))
        self.assertGreater(result['rmse'], 0)
    
    def test_calculate_volatility(self):
        """Test volatility calculation"""
        result = self.analyzer.analyze(self.sample_data, 'volatility')
        
        self.assertIn('daily_volatility', result)
        self.assertIn('annualized_volatility', result)
        self.assertIn('price_range', result)
        self.assertIn('coefficient_of_variation', result)
        self.assertIsInstance(result['daily_volatility'], (int, float))
    
    def test_calculate_basic_statistics(self):
        """Test basic statistics calculation"""
        result = self.analyzer.analyze(self.sample_data, 'statistics')
        
        required_stats = ['count', 'mean', 'median', 'std_dev', 'min', 'max', 'q25', 'q75']
        for stat in required_stats:
            self.assertIn(stat, result)
            self.assertIsInstance(result[stat], (int, float))
        
        # Verify logical relationships
        self.assertLessEqual(result['min'], result['q25'])
        self.assertLessEqual(result['q25'], result['median'])
        self.assertLessEqual(result['median'], result['q75'])
        self.assertLessEqual(result['q75'], result['max'])
    
    def test_analyze_price_trend(self):
        """Test price trend analysis"""
        result = self.analyzer.analyze(self.sample_data, 'price_trend')
        
        self.assertIn('trend_direction', result)
        self.assertIn('trend_strength', result)
        self.assertIn('slope_per_hour', result)
        self.assertIn('r_squared', result)
        
        self.assertIn(result['trend_direction'], ['increasing', 'decreasing'])
        self.assertIn(result['trend_strength'], ['strong', 'moderate', 'weak'])
        self.assertIsInstance(result['r_squared'], (int, float))
        self.assertGreaterEqual(result['r_squared'], 0)
        self.assertLessEqual(result['r_squared'], 1)
    
    def test_analyze_peak_hours(self):
        """Test peak hours analysis"""
        result = self.analyzer.analyze(self.sample_data, 'peak_analysis')
        
        self.assertIn('peak_average_price', result)
        self.assertIn('off_peak_average_price', result)
        self.assertIn('peak_premium', result)
        self.assertIn('peak_premium_percentage', result)
        self.assertIn('highest_price_hour', result)
        self.assertIn('lowest_price_hour', result)
        
        # Peak prices should generally be higher than off-peak
        self.assertGreaterEqual(result['peak_average_price'], result['off_peak_average_price'])
        self.assertGreaterEqual(result['peak_premium'], 0)
    
    def test_simple_forecast(self):
        """Test simple forecasting"""
        result = self.analyzer.analyze(self.sample_data, 'forecast', {'forecast_hours': 12})
        
        self.assertIn('forecast_method', result)
        self.assertIn('forecast_hours', result)
        self.assertIn('forecasted_prices', result)
        self.assertIn('base_price', result)
        
        self.assertEqual(result['forecast_hours'], 12)
        self.assertEqual(len(result['forecasted_prices']), 12)
        
        # All forecasted prices should be positive
        for price in result['forecasted_prices']:
            self.assertGreaterEqual(price, 0)
    
    def test_moving_average(self):
        """Test moving average calculation"""
        result = self.analyzer.analyze(self.sample_data, 'moving_average', {'window': 12})
        
        self.assertIn('moving_average', result)
        self.assertIn('window_hours', result)
        self.assertIn('trend', result)
        
        self.assertEqual(result['window_hours'], 12)
        self.assertIn(result['trend'], ['increasing', 'decreasing'])
        self.assertIsInstance(result['moving_average'], (int, float))
    
    def test_comprehensive_analysis(self):
        """Test comprehensive analysis"""
        result = self.analyzer.analyze(self.sample_data, 'comprehensive')
        
        # Should contain multiple analysis types
        expected_analyses = ['statistics', 'volatility', 'price_trend', 'peak_analysis']
        
        for analysis in expected_analyses:
            if analysis in result:
                self.assertIsInstance(result[analysis], dict)
                self.assertNotIn('error', result[analysis])
    
    def test_empty_data_handling(self):
        """Test handling of empty data"""
        empty_data = pd.DataFrame()
        result = self.analyzer.analyze(empty_data, 'statistics')
        
        self.assertIn('error', result)
    
    def test_missing_price_column(self):
        """Test handling of data without price column"""
        bad_data = pd.DataFrame({
            'timestamp': pd.date_range(start='2024-01-01', periods=10, freq='H'),
            'volume': [100] * 10
        })
        
        result = self.analyzer.analyze(bad_data, 'statistics')
        self.assertIn('error', result)
    
    def test_insufficient_data_for_forecast(self):
        """Test handling of insufficient data for forecasting"""
        small_data = self.sample_data.head(5)  # Only 5 data points
        result = self.analyzer.analyze(small_data, 'forecast', {'window': 24})
        
        self.assertIn('error', result)
    
    def test_correlation_analysis(self):
        """Test correlation analysis with multiple numeric columns"""
        # Add another numeric column for correlation
        self.sample_data['volume'] = np.random.normal(1000, 100, len(self.sample_data))
        
        result = self.analyzer.analyze(self.sample_data, 'correlation')
        
        if 'error' not in result:
            self.assertIn('correlations', result)
            self.assertIsInstance(result['correlations'], list)

if __name__ == '__main__':
    unittest.main()
